import { IsNotEmpty, <PERSON>Optional, IsString, IsUrl } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO for OAuth authorization request parameters
 */
export class AbsorbOAuthAuthorizationDto {
  @ApiProperty({
    description: 'The unique API client identifier from Portal Settings',
    example: '_ahKweh2tdKdNMKnecwo',
  })
  @IsString()
  @IsNotEmpty()
  client_id: string;

  @ApiProperty({
    description: 'The unique API client secret from Portal Settings',
    example: 'iBHVbc0fn7vdCgvMxDAy7fWNlpQCwAcQoeCNYEDboahcUime01',
  })
  @IsString()
  @IsNotEmpty()
  client_secret: string;

  @ApiProperty({
    description: 'The URI users are sent back to after authorization (must be HTTPS)',
    example: 'https://example.com/callback',
  })
  @IsUrl({ require_tld: false, protocols: ['https'] })
  redirect_uri: string;

  @ApiProperty({
    description: 'Response type - always "code" for authorization code flow',
    example: 'code',
    default: 'code',
  })
  @IsString()
  @IsNotEmpty()
  response_type = 'code';

  @ApiProperty({
    description: 'Space-delimited list of permissions - supported scope: "admin.v1"',
    example: 'admin.v1',
    default: 'admin.v1',
  })
  @IsString()
  @IsNotEmpty()
  scope = 'admin.v1';

  @ApiProperty({
    description: 'A unique string value that is hard to guess for security',
    example: 'randomStateString123',
  })
  @IsString()
  @IsNotEmpty()
  state: string;
}

/**
 * DTO for OAuth token exchange request
 */
export class AbsorbOAuthTokenRequestDto {
  @ApiProperty({
    description:
      'Grant type - "authorization_code" for initial token or "refresh_token" for refresh',
    example: 'authorization_code',
    enum: ['authorization_code', 'refresh_token'],
  })
  @IsString()
  @IsNotEmpty()
  grant_type: 'authorization_code' | 'refresh_token';

  @ApiProperty({
    description: 'The unique API client identifier',
    example: '_ahKweh2tdKdNMKnecwo',
  })
  @IsString()
  @IsNotEmpty()
  client_id: string;

  @ApiProperty({
    description: 'The unique API client secret',
    example: 'iBHVbc0fn7vdCgvMxDAy7fWNlpQCwAcQoeCNYEDboahcUime01',
  })
  @IsString()
  @IsNotEmpty()
  client_secret: string;

  @ApiProperty({
    description: 'String value to associate client session with ID Token',
    example: 'test',
  })
  @IsString()
  @IsNotEmpty()
  nonce: string;

  @ApiProperty({
    description: 'Authorization code from previous step (required for authorization_code grant)',
    example: 'auth_code_123',
    required: false,
  })
  @IsOptional()
  @IsString()
  code?: string;

  @ApiProperty({
    description: 'Refresh token for getting new access token (required for refresh_token grant)',
    example: '09RR0D5mp5J56i4GTPEjdhMyMhw6t5IhTad5ErDD',
    required: false,
  })
  @IsOptional()
  @IsString()
  refresh_token?: string;

  @ApiProperty({
    description: 'Redirect URI used in authorization request',
    example: 'https://example.com/callback',
    required: false,
  })
  @IsOptional()
  @IsUrl({ require_tld: false, protocols: ['https'] })
  redirect_uri?: string;

  @ApiProperty({
    description: 'Username to sign in as (for on-behalf-of functionality)',
    example: '<EMAIL>',
    required: false,
  })
  @IsOptional()
  @IsString()
  onBehalfOf?: string;

  @ApiProperty({
    description: 'Space-delimited list of permissions (for refresh token requests)',
    example: 'admin.v1',
    required: false,
  })
  @IsOptional()
  @IsString()
  scope?: string;
}

/**
 * DTO for OAuth token response
 */
export class AbsorbOAuthTokenResponseDto {
  @ApiProperty({
    description: 'The access token for API requests',
    example: 'eyJhbGciOiJSUzI1NiIsImtpZCI6Im9HZTc1M0VNdVhFUFhmQ1hKUWlxaHZhNHUxbyI...',
  })
  access_token: string;

  @ApiProperty({
    description: 'Token type - always "bearer"',
    example: 'bearer',
  })
  token_type: string;

  @ApiProperty({
    description: 'Number of seconds until token expires (4 hours = 14400 seconds)',
    example: 14399,
  })
  expires_in: number;

  @ApiProperty({
    description: 'Refresh token for obtaining new access tokens (valid for 1 year)',
    example: '09RR0D5mp5J56i4GTPEjdhMyMhw6t5IhTad5ErDD',
  })
  refresh_token: string;
}

/**
 * DTO for OAuth authorization URL response
 */
export class AbsorbOAuthAuthorizationUrlDto {
  @ApiProperty({
    description: 'The authorization URL to redirect users to',
    example: 'https://rest.myabsorb.com/oauth/authorize?client_id=...&response_type=code&...',
  })
  authorization_url: string;

  @ApiProperty({
    description: 'The state parameter for security verification',
    example: 'randomStateString123',
  })
  state: string;
}

/**
 * DTO for OAuth error response
 */
export class AbsorbOAuthErrorResponseDto {
  @ApiProperty({
    description: 'The specific error code indicating the issue',
    example: 'invalid_request',
  })
  error: string;

  @ApiProperty({
    description: 'Human-readable error description',
    example: 'The request is missing a required parameter',
    required: false,
  })
  error_description?: string;
}

/**
 * DTO for OAuth callback query parameters
 */
export class AbsorbOAuthCallbackDto {
  @ApiProperty({
    description: 'Authorization code returned by Absorb after user consent',
    example: 'auth_code_123456',
    required: false,
  })
  @IsOptional()
  @IsString()
  code?: string;

  @ApiProperty({
    description: 'State parameter for CSRF protection (must match original request)',
    example: 'randomStateString123',
    required: false,
  })
  @IsOptional()
  @IsString()
  state?: string;

  @ApiProperty({
    description: 'Error code if authorization failed',
    example: 'access_denied',
    required: false,
  })
  @IsOptional()
  @IsString()
  error?: string;

  @ApiProperty({
    description: 'Human-readable error description',
    example: 'The user denied the request',
    required: false,
  })
  @IsOptional()
  @IsString()
  error_description?: string;
}

/**
 * DTO for OAuth callback response
 */
export class AbsorbOAuthCallbackResponseDto {
  @ApiProperty({
    description: 'Indicates if the OAuth callback was successful',
    example: true,
  })
  success: boolean;

  @ApiProperty({
    description: 'Success or error message',
    example: 'Authorization successful. Tokens have been exchanged.',
  })
  message: string;

  @ApiProperty({
    description: 'OAuth tokens (only present on success)',
    type: AbsorbOAuthTokenResponseDto,
    required: false,
  })
  tokens?: AbsorbOAuthTokenResponseDto;

  @ApiProperty({
    description: 'Error details (only present on failure)',
    required: false,
  })
  error?: {
    code: string;
    description?: string;
  };
}

/**
 * DTO for OAuth configuration
 */
export class AbsorbOAuthConfigDto {
  @ApiProperty({
    description: 'OAuth client ID from Portal Settings',
    example: '_ahKweh2tdKdNMKnecwo',
  })
  @IsString()
  @IsNotEmpty()
  client_id: string;

  @ApiProperty({
    description: 'OAuth client secret from Portal Settings',
    example: 'iBHVbc0fn7vdCgvMxDAy7fWNlpQCwAcQoeCNYEDboahcUime01',
  })
  @IsString()
  @IsNotEmpty()
  client_secret: string;

  @ApiProperty({
    description: 'Redirect URI for OAuth callback',
    example: 'https://example.com/callback',
  })
  @IsUrl({ require_tld: false, protocols: ['https'] })
  redirect_uri: string;

  @ApiProperty({
    description: 'Absorb API base URL',
    example: 'https://rest.myabsorb.com',
  })
  @IsUrl()
  base_url: string;
}
