# Development environment variables template
# Copy this file to .env.development and fill in your actual values

# Application
NODE_ENV=development
PORT=3000
API_PREFIX=api/v1

# Database
DATABASE_URL=postgresql://localhost:5432/psychscene_bridge_dev
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_NAME=psychscene_bridge_dev
DATABASE_USERNAME=your_db_username
DATABASE_PASSWORD=your_db_password

# JWT
JWT_SECRET=your-development-jwt-secret-key
JWT_EXPIRES_IN=7d

# CORS
CORS_ORIGIN=http://localhost:3000,http://localhost:3001

# Logging
LOG_LEVEL=debug

# Rate Limiting
THROTTLE_TTL=60
THROTTLE_LIMIT=100

# Sentry Error Tracking
SENTRY_DSN=https://<EMAIL>/project-id
SENTRY_ENVIRONMENT=development
SENTRY_ENABLED=false
SENTRY_TRACES_SAMPLE_RATE=0.1
SENTRY_PROFILES_SAMPLE_RATE=0.1
SENTRY_ENABLE_LOGS=false

# Development specific
ENABLE_SWAGGER=true
ENABLE_DEBUG=true
# Absorb LMS Integration Configuration
# Copy this file to your environment-specific .env file and fill in the actual values


# Absorb LMS configuration  Absorb Integration API Configuration
ABSORB_API_BASE_URL=https://rest.myabsorb.com.au
ABSORB_API_KEY=your_absorb_api_key
ABSORB_API_VERSION=v2
ABSORB_API_TIMEOUT=30000
ABSORB_RETRY_ATTEMPTS=3
ABSORB_RETRY_DELAY=1000

# Absorb OAuth Configuration
ABSORB_OAUTH_CLIENT_ID=your_absorb_oauth_client_id
ABSORB_OAUTH_CLIENT_SECRET=your_absorb_oauth_client_secret
ABSORB_OAUTH_REDIRECT_URI=https://your-domain.com/auth/absorb/callback
ABSORB_OAUTH_BASE_URL=https://rest.myabsorb.com

# Absorb API Configuration
ABSORB_API_VERSION=v2
ABSORB_API_TIMEOUT=30000
ABSORB_RETRY_ATTEMPTS=3
ABSORB_RETRY_DELAY=1000

# Absorb Webhook Configuration
ABSORB_WEBHOOK_SECRET=your_webhook_secret_key
ABSORB_WEBHOOK_ENABLED=true
ABSORB_WEBHOOK_TIMESTAMP_TOLERANCE_MS=300000
ABSORB_WEBHOOK_MAX_RETRIES=3
ABSORB_WEBHOOK_RETRY_DELAY_MS=1000

