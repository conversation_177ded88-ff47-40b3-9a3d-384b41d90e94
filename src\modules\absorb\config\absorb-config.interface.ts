/**
 * Absorb authentication credentials interface
 */
export interface IAbsorbAuthCredentials {
  username: string;
  password: string;
  privateKey: string;
}

/**
 * Absorb OAuth configuration interface
 */
export interface IAbsorbOAuthConfig {
  baseUrl: string;
  clientId: string;
  clientSecret: string;
  redirectUri: string;
}

/**
 * Absorb Integration API configuration interface
 */
export interface IAbsorbIntegrationApiConfig {
  baseUrl: string;
  timeout?: number;
  retryAttempts?: number;
  retryDelay?: number;
}

/**
 * Absorb webhook configuration interface
 * Aligned with official AbsorbLMS webhook requirements
 */
export interface IAbsorbWebhookConfig {
  /** Webhook secret for HMAC-SHA256 signature verification (required) */
  secret: string;
  /** Enable signature verification using 'absorb-signature' header */
  enableSignatureVerification: boolean;
  /** Enable timestamp validation to prevent replay attacks */
  enableTimestampValidation: boolean;
  /** Timestamp tolerance in milliseconds (default: 300000 = 5 minutes) */
  timestampToleranceMs?: number;
}

/**
 * Absorb LMS configuration interface
 */
export interface IAbsorbLmsConfig {
  /** Integration API configuration */
  integrationApi?: IAbsorbIntegrationApiConfig;
  /** Base URL for Absorb LMS API */
  apiBaseUrl: string;
  /** Authentication credentials */
  auth: IAbsorbAuthCredentials;
  /** API key for authentication */
  apiKey: string;
  /** API version (e.g., 'v2') */
  apiVersion: string;
  /** OAuth configuration (optional) */
  oauth?: IAbsorbOAuthConfig;
  /** Webhook configuration */
  webhook?: IAbsorbWebhookConfig;
}
