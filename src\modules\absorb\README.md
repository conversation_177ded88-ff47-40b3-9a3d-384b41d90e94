# Absorb LMS Integration Module

This module provides integration with Absorb LMS, including authentication and API access functionality.

## Authentication Controller

The `AbsorbAuthController` provides endpoints for managing authentication with the Absorb LMS API.

### Endpoints

#### POST `/api/v1/absorb/auth/login`

Authenticate with Absorb LMS using credentials.

**Request Body:**

```json
{
  "username": "<EMAIL>",
  "password": "your-password",
  "privateKey": "your-private-key"
}
```

**Response:**

```json
{
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "expiresAt": "2024-12-20T10:30:00Z",
  "tokenType": "Bearer"
}
```

#### POST `/api/v1/absorb/auth/refresh`

Refresh the current access token.

**Response:**

```json
{
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "expiresAt": "2024-12-20T11:30:00Z",
  "tokenType": "Bearer"
}
```

#### GET `/api/v1/absorb/auth/status`

Get current authentication status.

**Response:**

```json
{
  "isAuthenticated": true,
  "expiresAt": "2024-12-20T10:30:00Z",
  "timeUntilExpiry": 3600
}
```

#### POST `/api/v1/absorb/auth/logout`

Logout and invalidate the current token.

**Response:**

```json
{
  "message": "Successfully logged out"
}
```

## Authentication Service

The `AbsorbAuthService` provides the following methods:

- `authenticate(credentials)` - Authenticate with Absorb LMS
- `refreshToken()` - Refresh the current token
- `getAuthStatus()` - Get authentication status
- `logout()` - Clear stored token
- `getCurrentToken()` - Get current valid token
- `getAuthorizationHeader()` - Get authorization header for API requests
- `isAuthenticated()` - Check if currently authenticated

## Token Storage

Tokens are stored in memory within the service instance. The service automatically:

- Validates token expiration
- Clears expired tokens
- Provides helper methods for API authorization

## Configuration

Ensure the following environment variables are configured:

- `ABSORB_API_BASE_URL` - Base URL for Absorb API
- `ABSORB_API_KEY` - API key for requests
- `ABSORB_API_VERSION` - API version (default: 2)

## Webhook Controller

The `AbsorbWebhookController` provides endpoints for receiving and processing webhook events from Absorb LMS.

### Endpoints

#### GET `/api/v1/absorb/webhooks`

Webhook verification endpoint for Absorb LMS setup.

**Query Parameters:**
- `challenge` - Verification challenge string from Absorb LMS

**Response:**
Returns the challenge string to verify the webhook endpoint.

#### POST `/api/v1/absorb/webhooks`

Process incoming webhook events from Absorb LMS.

**Headers:**
- `X-Absorb-Signature` - Webhook signature for verification (optional)
- `X-Absorb-Timestamp` - Event timestamp for replay attack prevention (optional)

**Request Body:**

```json
{
  "id": "wh_1234567890abcdef",
  "event": "user.created",
  "timestamp": "2024-12-20T10:30:00Z",
  "data": {
    "userId": 123,
    "email": "<EMAIL>",
    "firstName": "John",
    "lastName": "Doe"
  },
  "apiVersion": "2.0",
  "source": "absorb-lms"
}
```

**Response:**

```json
{
  "status": "success",
  "message": "Webhook processed successfully",
  "eventId": "wh_1234567890abcdef"
}
```

#### GET `/api/v1/absorb/webhooks/health`

Health check endpoint for webhook monitoring.

**Response:**

```json
{
  "status": "healthy",
  "timestamp": "2024-12-20T10:30:00Z"
}
```

## Webhook Service

The `AbsorbWebhookService` provides the following functionality:

- `verifyWebhookSignature(payload, signature)` - Verify webhook signature authenticity
- `processWebhookEvent(payload)` - Process incoming webhook events
- Event routing to specific handlers based on event type
- Comprehensive logging and error handling

### Supported Webhook Events

- **User Events:**
  - `user.created` - New user created in Absorb LMS
  - `user.updated` - User information updated
  - `user.deleted` - User deleted from system

- **Enrollment Events:**
  - `enrollment.created` - User enrolled in a course
  - `enrollment.completed` - User completed a course
  - `enrollment.failed` - User failed a course

- **Course Events:**
  - `course.created` - New course created
  - `course.updated` - Course information updated
  - `course.deleted` - Course deleted

- **Certificate Events:**
  - `certificate.issued` - Certificate issued to user

- **Session Events:**
  - `session.created` - Training session created
  - `session.updated` - Training session updated

## Webhook Configuration

Add the following environment variables for webhook functionality:

- `ABSORB_WEBHOOK_SECRET` - Secret key for webhook signature verification (optional)
- `ABSORB_WEBHOOK_ENABLE_SIGNATURE_VERIFICATION` - Enable/disable signature verification (default: true)
- `ABSORB_WEBHOOK_ENABLE_TIMESTAMP_VALIDATION` - Enable/disable timestamp validation (default: true)

## Usage Example

```typescript
import { AbsorbAuthService } from './services/absorb-auth.service';
import { AbsorbWebhookService } from './services/absorb-webhook.service';

// Inject the services
constructor(
  private authService: AbsorbAuthService,
  private webhookService: AbsorbWebhookService
) {}

// Authenticate
const authResult = await this.authService.authenticate({
  username: '<EMAIL>',
  password: 'password',
  privateKey: 'private-key'
});

// Check if authenticated
const isAuth = await this.authService.isAuthenticated();

// Get authorization header for API calls
const authHeader = await this.authService.getAuthorizationHeader();

// Process webhook event
const webhookResult = await this.webhookService.processWebhookEvent({
  id: 'wh_123',
  event: 'user.created',
  timestamp: new Date().toISOString(),
  data: { userId: 123, email: '<EMAIL>' }
});
```

## Webhook Security

The webhook implementation includes several security features:

1. **Signature Verification**: Validates webhook authenticity using HMAC-SHA256
2. **Timestamp Validation**: Prevents replay attacks by checking event timestamps
3. **Input Validation**: Validates all incoming webhook payloads using DTOs
4. **Error Handling**: Comprehensive error handling with proper logging
