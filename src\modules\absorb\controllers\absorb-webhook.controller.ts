import { <PERSON>, Lo<PERSON>, <PERSON>, Req, <PERSON>s, UseGuards } from '@nestjs/common';
import type { Request, Response } from 'express';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { AbsorbWebhookProcessingService } from '../services/webhook/absorb-webhook-processing.service';
import { WebhookHeadersValidationGuard } from '../guards/webhook-headers-validation.guard';
import { generateCorrelationId } from '../utils';

/**
 * AbsorbLMS Webhook Controller
 * Handles incoming webhooks from AbsorbLMS with comprehensive security validation
 */
@ApiTags('absorb-webhooks')
@Controller('absorb/webhooks')
export class AbsorbWebhookController {
  private readonly logger = new Logger(AbsorbWebhookController.name);

  constructor(private readonly webhookProcessingService: AbsorbWebhookProcessingService) {}

  @Post()
  @UseGuards(WebhookHeadersValidationGuard)
  @ApiOperation({ summary: 'Handle Absorb LMS webhook events' })
  @ApiResponse({ status: 200, description: 'Webhook processed successfully' })
  @ApiResponse({ status: 400, description: 'Invalid webhook payload or signature' })
  @ApiResponse({ status: 401, description: 'Unauthorized - Invalid signature' })
  @ApiResponse({ status: 500, description: 'Internal server error during webhook processing' })
  async handleWebhook(@Req() request: Request, @Res() response: Response): Promise<void> {
    const correlationId = generateCorrelationId();
    const startTime = Date.now();

    // Add correlation ID to request for downstream services
    (request as any).correlationId = correlationId;

    this.logger.log('Webhook request received', {
      correlationId,
      userAgent: request.headers['user-agent'],
      contentType: request.headers['content-type'],
      ip: request.ip,
    });

    try {
      // Delegate all processing to the service layer
      const result = await this.webhookProcessingService.processWebhook(request, response);

      // Send response based on processing result
      const statusCode = result.success ? 200 : 500;
      response.status(statusCode).json({
        ...result,
        correlationId,
        processingTime: Date.now() - startTime,
      });
      this.logger.log(
        `Webhook ${result.success ? 'processed successfully' : 'processing failed'}`,
        {
          correlationId,
          success: result.success,
          processingTime: Date.now() - startTime,
          eventType: result.eventType,
        },
      );
    } catch (error) {
      const processingTime = Date.now() - startTime;

      this.logger.error('Unexpected error in webhook controller', {
        correlationId,
        processingTime,
        error: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined,
      });

      response.status(500).json({
        success: false,
        correlationId,
        processingTime,
        message: 'Internal server error',
        error: 'Unexpected error occurred',
      });
    }
  }
}
