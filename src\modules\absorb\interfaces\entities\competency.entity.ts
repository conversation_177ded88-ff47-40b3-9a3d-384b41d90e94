/**
 * Competency entity schema
 */
export interface CompetencyEntity {
  readonly id: string; // GUID
  readonly name: string;
  readonly competencyLevel: number; // int32
  readonly categoryName?: string;
  readonly dateAcquired?: string; // ISO 8601 datetime
}

/**
 * Certificate entity (basic structure)
 */
export interface CertificateEntity {
  readonly id: string; // GUID
  readonly name: string;
  readonly url?: string;
  readonly dateAwarded: string; // ISO 8601 datetime
  readonly expiryDate?: string; // ISO 8601 datetime
  readonly userId: string; // GUID - learner who earned certificate
  readonly courseId: string; // GUID - course that awarded certificate
}

/**
 * Skill path item (Skills subscription required)
 */
export interface SkillPathItem {
  readonly id: string; // GUID
  readonly name: string;
  readonly userRating: number; // int32
  readonly skillType: string; // "Skill"
  readonly skillCategoryId?: string; // GUID
  readonly courseCount: number; // int32
  readonly courseIds: string[]; // Array of GUIDs
  readonly completedCourseCount: number; // int32
  readonly skillPathUpdateRequired: boolean;
}

/**
 * Competency path item
 */
export interface CompetencyPathItem {
  readonly id: string; // GUID
  readonly name: string;
  readonly userRating: number; // int32
  readonly skillType: string;
  readonly skillCategoryId?: string; // GUID
  readonly courseCount: number; // int32
  readonly courseIds: string[]; // Array of GUIDs
  readonly completedCourseCount: number; // int32
  readonly skillPathUpdateRequired: boolean;
}

/**
 * Learning path entity
 */
export interface LearningPathEntity {
  readonly id: string; // GUID
  readonly roleName: string;
  readonly courseCount: number; // int32
  readonly skills: SkillPathItem[];
  readonly competencies: CompetencyPathItem[];
}
