import { WebhookEventType } from '@/modules/absorb/enums';
import { BaseWebhookPayload } from '../absorb-base-webhook-payload.interface';

/**
 * Competency event payloads
 */
export interface CompetencyAwardedPayload extends BaseWebhookPayload {
  readonly eventType: WebhookEventType.COMPETENCY_AWARDED;
  readonly competencyId: string; // GUID - competency identifier
  readonly competencyLevel?: number; // int32 - achievement level
  readonly dateAwarded: string; // ISO 8601 datetime
}
