import { Injectable } from '@nestjs/common';
import { ConfigService as NestConfigService } from '@nestjs/config';
import {
  IAppConfig,
  IConfig,
  ICorsConfig,
  IDatabaseConfig,
  IFeatureConfig,
  IJwtConfig,
  ILoggingConfig,
  ISentryConfig,
  IThrottleConfig,
} from './config.interface';
import { IAbsorbLmsConfig } from '@/modules/absorb/config';

/**
 * Configuration service that provides typed access to environment variables
 */
@Injectable()
export class ConfigService {
  constructor(private readonly _nestConfigService: NestConfigService) {}

  /**
   * Get application configuration
   */
  get app(): IAppConfig {
    return {
      nodeEnv: this._nestConfigService.get<string>('NODE_ENV', 'development'),
      port: this._nestConfigService.get<number>('PORT', 3000),
      apiPrefix: this._nestConfigService.get<string>('API_PREFIX', 'api/v1'),
    };
  }

  /**
   * Get database configuration
   */
  get database(): IDatabaseConfig {
    return {
      url: this._nestConfigService.get<string>('DATABASE_URL', ''),
      host: this._nestConfigService.get<string>('DATABASE_HOST', 'localhost'),
      port: this._nestConfigService.get<number>('DATABASE_PORT', 5432),
      name: this._nestConfigService.get<string>('DATABASE_NAME', ''),
      username: this._nestConfigService.get<string>('DATABASE_USERNAME', ''),
      password: this._nestConfigService.get<string>('DATABASE_PASSWORD', ''),
    };
  }

  /**
   * Get JWT configuration
   */
  get jwt(): IJwtConfig {
    return {
      secret: this._nestConfigService.get<string>('JWT_SECRET', ''),
      expiresIn: this._nestConfigService.get<string>('JWT_EXPIRES_IN', '1d'),
    };
  }

  /**
   * Get CORS configuration
   */
  get cors(): ICorsConfig {
    const origin = this._nestConfigService.get<string>('CORS_ORIGIN', 'http://localhost:3000');
    return {
      origin: origin.includes(',') ? origin.split(',').map(o => o.trim()) : origin,
    };
  }

  /**
   * Get logging configuration
   */
  get logging(): ILoggingConfig {
    return {
      level: this._nestConfigService.get<string>('LOG_LEVEL', 'info'),
      prettyPrint: this._nestConfigService.get<boolean>('LOG_PRETTY_PRINT', this.isDevelopment),
      colorize: this._nestConfigService.get<boolean>('LOG_COLORIZE', this.isDevelopment),
      translateTime: this._nestConfigService.get<boolean>('LOG_TRANSLATE_TIME', true),
      ignore: this._nestConfigService.get<string>('LOG_IGNORE', 'pid,hostname'),
      redact: this._nestConfigService
        .get<string>('LOG_REDACT', 'password,token,secret,authorization')
        .split(','),
    };
  }

  /**
   * Get throttle configuration
   */
  get throttle(): IThrottleConfig {
    return {
      ttl: this._nestConfigService.get<number>('THROTTLE_TTL', 60),
      limit: this._nestConfigService.get<number>('THROTTLE_LIMIT', 10),
    };
  }

  /**
   * Get feature flags configuration
   */
  get features(): IFeatureConfig {
    return {
      enableSwagger: this._nestConfigService.get<boolean>('ENABLE_SWAGGER', true),
      enableDebug: this._nestConfigService.get<boolean>('ENABLE_DEBUG', false),
    };
  }

  /**
   * Get Sentry configuration
   */
  get sentry(): ISentryConfig {
    return {
      dsn: this._nestConfigService.get<string>('SENTRY_DSN', ''),
      environment: this._nestConfigService.get<string>('SENTRY_ENVIRONMENT', 'development'),
      enabled: this._nestConfigService.get<boolean>('SENTRY_ENABLED', false),
      tracesSampleRate: this._nestConfigService.get<number>('SENTRY_TRACES_SAMPLE_RATE', 0.1),
      profilesSampleRate: this._nestConfigService.get<number>('SENTRY_PROFILES_SAMPLE_RATE', 0.1),
      enableLogs: this._nestConfigService.get<boolean>('SENTRY_ENABLE_LOGS', false),
    };
  }

  /**
   * Get Absorb LMS configuration
   */
  get absorb(): IAbsorbLmsConfig {
    return {
      apiBaseUrl: this._nestConfigService.get<string>('ABSORB_API_BASE_URL', ''),
      apiKey: this._nestConfigService.get<string>('ABSORB_API_KEY', ''),
      apiVersion: this._nestConfigService.get<string>('ABSORB_API_VERSION', ''),
      auth: {
        username: this._nestConfigService.get<string>('ABSORB_USERNAME', ''),
        password: this._nestConfigService.get<string>('ABSORB_PASSWORD', ''),
        privateKey: this._nestConfigService.get<string>('ABSORB_PRIVATE_KEY', ''),
      },
      oauth: {
        baseUrl: this._nestConfigService.get<string>('ABSORB_OAUTH_BASE_URL', ''),
        clientId: this._nestConfigService.get<string>('ABSORB_OAUTH_CLIENT_ID', ''),
        clientSecret: this._nestConfigService.get<string>('ABSORB_OAUTH_CLIENT_SECRET', ''),
        redirectUri: this._nestConfigService.get<string>('ABSORB_OAUTH_REDIRECT_URI', ''),
      },
    };
  }

  /**
   * Get all configuration
   */
  get all(): IConfig {
    return {
      app: this.app,
      database: this.database,
      jwt: this.jwt,
      cors: this.cors,
      logging: this.logging,
      throttle: this.throttle,
      features: this.features,
      sentry: this.sentry,
      absorb: this.absorb,
    };
  }

  /**
   * Check if the application is running in production
   */
  get isProduction(): boolean {
    return this.app.nodeEnv === 'production';
  }

  /**
   * Check if the application is running in development
   */
  get isDevelopment(): boolean {
    return this.app.nodeEnv === 'development';
  }

  /**
   * Check if the application is running in staging
   */
  get isStaging(): boolean {
    return this.app.nodeEnv === 'staging';
  }

  /**
   * Check if the application is running in test
   */
  get isTest(): boolean {
    return this.app.nodeEnv === 'test';
  }
}
