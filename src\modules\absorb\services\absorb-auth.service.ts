import { Injectable, Logger, OnModuleInit, UnauthorizedException } from '@nestjs/common';
import { AbsorbAuthCredentialsDto, AbsorbAuthResponseDto } from '../dto/auth.dto';
import { ConfigService } from '@/config';
import { IAbsorbLmsConfig } from '../config';

/**
 * Absorb authentication service
 * This service handles authentication and authorization for Absorb LMS integration
 * Following NestJS best practices with proper logger usage and configuration validation
 */
@Injectable()
export class AbsorbAuthService implements OnModuleInit {
  private readonly logger = new Logger(AbsorbAuthService.name);
  constructor(private readonly configService: ConfigService) {}

  /**
   * Validate configuration on module initialization
   */
  onModuleInit(): void {
    this.validateConfiguration();
  }

  /**
   * Authenticate with Absorb LMS using credentials from configuration
   * @param credentials Optional credentials to override config values
   * @returns Authentication token and metadata
   */
  async authenticate(credentials?: AbsorbAuthCredentialsDto): Promise<AbsorbAuthResponseDto> {
    try {
      this.logger.log('Authenticating with Absorb LMS');

      const { authCredentials, apiKey, baseUrl } = this.prepareAuthenticationData(credentials);
      this.validateAuthenticationConfig(baseUrl, authCredentials, apiKey);

      const authResponse = await this.performAuthenticationRequest(
        baseUrl,
        authCredentials,
        apiKey,
      );
      const tokenData = await this.processAuthenticationResponse(authResponse);

      return tokenData;
    } catch (error: unknown) {
      return this.handleAuthenticationError(error);
    }
  }

  /**
   * Validate required configuration on startup
   */
  private validateConfiguration(): void {
    const config = this.configService.absorb;

    this.validateMainConfigFields(config);
    this.validateAuthCredentials(config);

    this.logger.log('Absorb configuration validated successfully');
  }

  /**
   * Validate main configuration fields
   */
  private validateMainConfigFields(config: IAbsorbLmsConfig): void {
    this.validateConfigField(config?.apiBaseUrl, 'apiBaseUrl');
    this.validateConfigField(config?.apiKey, 'apiKey');
  }

  /**
   * Validate authentication credentials
   */
  private validateAuthCredentials(config: IAbsorbLmsConfig): void {
    if (!config?.auth) {
      const errorMessage = 'Missing required Absorb configuration: auth credentials';
      this.logger.error(errorMessage);
      throw new Error(errorMessage);
    }

    const authFields = ['username', 'password', 'privateKey'] as const;
    for (const field of authFields) {
      this.validateAuthField(config.auth[field], field);
    }
  }

  /**
   * Validate a single configuration field
   */
  private validateConfigField(value: string, fieldName: string): void {
    if (!value || typeof value !== 'string' || value.trim() === '') {
      const errorMessage = `Missing required Absorb configuration: ${fieldName}`;
      this.logger.error(errorMessage);
      throw new Error(errorMessage);
    }
  }

  /**
   * Validate a single auth field
   */
  private validateAuthField(value: string, fieldName: string): void {
    if (!value || typeof value !== 'string' || value.trim() === '') {
      const errorMessage = `Missing required Absorb auth configuration: ${fieldName}`;
      this.logger.error(errorMessage);
      throw new Error(errorMessage);
    }
  }

  private prepareAuthenticationData(credentials?: AbsorbAuthCredentialsDto): {
    authCredentials: { username: string; password: string; privateKey: string };
    apiKey: string;
    baseUrl: string;
  } {
    const absorbConfig = this.configService.absorb;

    console.log(absorbConfig);

    const authCredentials = {
      username: credentials?.username ?? absorbConfig.auth.username,
      password: credentials?.password ?? absorbConfig.auth.password,
      privateKey: credentials?.privateKey ?? absorbConfig.auth.privateKey,
    };

    return {
      authCredentials,
      apiKey: absorbConfig.apiKey,
      baseUrl: absorbConfig.apiBaseUrl,
    };
  }

  private validateAuthenticationConfig(
    baseUrl: string,
    authCredentials: { username: string; password: string; privateKey: string },
    apiKey: string,
  ): void {
    if (!baseUrl) {
      throw new UnauthorizedException('Absorb API base URL not configured');
    }
    if (!authCredentials.username || !authCredentials.password || !authCredentials.privateKey) {
      throw new UnauthorizedException('Absorb authentication credentials not configured');
    }
    if (!apiKey) {
      throw new UnauthorizedException('Absorb API key not configured');
    }
  }

  private async performAuthenticationRequest(
    baseUrl: string,
    authCredentials: { username: string; password: string; privateKey: string },
    apiKey: string,
  ): Promise<Response> {
    const authUrl = `${baseUrl}/authenticate`;
    const authPayload = {
      username: authCredentials.username,
      password: authCredentials.password,
      privateKey: authCredentials.privateKey,
    };

    this.logger.debug(`Authenticating with Absorb API at ${authUrl}`);

    const response = await fetch(authUrl, {
      method: 'POST',
      headers: {
        'x-api-key': apiKey,
        'Content-Type': 'application/json',
        'x-api-version': '2',
      },
      body: JSON.stringify(authPayload),
    });

    if (!response.ok) {
      const errorText = await response.text();
      this.logger.error('Authentication failed', {
        status: response.status,
        response: errorText,
        authUrl,
      });
      throw new UnauthorizedException('Invalid credentials or authentication failed');
    }

    return response;
  }

  private async processAuthenticationResponse(response: Response): Promise<AbsorbAuthResponseDto> {
    this.logger.debug('Processing authentication response');

    const authData = (await response.json()) as string;
    const expirationTime = new Date(Date.now() + 1000 * 60 * 60 * 24); // 24 hours

    this.logger.log(`Authentication successful, token expires at ${expirationTime.toISOString()}`);

    return {
      token: authData,
      expiresAt: expirationTime.toISOString(),
      tokenType: 'Bearer',
    };
  }

  private handleAuthenticationError(error: unknown): never {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    const errorType = error instanceof Error ? error.constructor.name : 'Unknown';

    this.logger.error('Authentication error occurred', {
      errorMessage,
      errorType,
      stack: error instanceof Error ? error.stack : undefined,
    });

    if (error instanceof UnauthorizedException) {
      throw error;
    }
    throw new UnauthorizedException('Authentication failed due to network or server error');
  }
}
