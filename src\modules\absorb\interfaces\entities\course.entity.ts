import { ActiveStatusEnum, CompletionTypeEnum, ExpireTypeEnum } from '../../enums';
import { CourseType, DurationObject, PriceResource } from '../../types';

/**
 * Base course entity interface
 */
export interface CourseEntity {
  // Core identifiers
  readonly id: string; // GUID - unique course identifier
  readonly courseType: CourseType;

  // Basic information
  readonly name: string; // course title
  readonly description?: string; // HTML supported
  readonly notes?: string; // additional notes
  readonly externalId?: string; // external system reference

  // Scheduling & access
  readonly accessDate?: string; // ISO 8601 datetime
  readonly expireType: ExpireTypeEnum;
  readonly expireDuration?: DurationObject; // when using duration-based expiry
  readonly expiryDate?: string; // ISO 8601 datetime - when using specific date
  readonly activeStatus: ActiveStatusEnum;

  // Content & organization
  readonly tagIds?: string[]; // Array of GUID tag identifiers
  readonly resourceIds?: string[]; // Array of GUID resource identifiers
  readonly editorIds?: string[]; // Array of GUID editor user identifiers
  readonly categoryId?: string; // GUID - course category

  // Pricing
  readonly prices?: PriceResource[];
  readonly companyCost?: number; // decimal
  readonly learnerCost?: number; // decimal
  readonly companyTime?: number; // decimal - estimated time cost
  readonly learnerTime?: number; // decimal - estimated time cost

  // Learning & prerequisites
  readonly competencyDefinitionIds?: string[]; // Array of GUID competency identifiers
  readonly prerequisiteCourseIds?: string[]; // Array of GUID prerequisite course identifiers
  readonly postEnrollmentCourseIds?: string[]; // Array of GUID post-enrollment course identifiers
  readonly allowCourseEvaluation: boolean;

  // Certificate & completion
  readonly certificateUrl?: string;
  readonly audience?: string; // target audience description
  readonly goals?: string; // learning objectives
  readonly vendor?: string; // content vendor

  // Audit fields
  readonly dateEdited: string; // ISO 8601 datetime
  readonly dateAdded: string; // ISO 8601 datetime
}

/**
 * Online course specific extensions
 */
export interface OnlineCourseEntity extends CourseEntity {
  readonly chapterIds?: string[]; // Array of GUID chapter identifiers
  readonly showMobileAlert: boolean;
  readonly completionType: CompletionTypeEnum;
}

/**
 * Instructor-led course extensions
 */
export interface InstructorLedCourseEntity extends CourseEntity {
  readonly sessionIds?: string[]; // Array of GUID session identifiers
  readonly requireSessionOnPurchase: boolean;
  readonly allowCommentsPerSession: boolean;
}

/**
 * Curriculum extensions
 */
export interface CurriculumEntity extends CourseEntity {
  readonly isPacingEnabled: boolean;
  readonly curriculumGroupIds?: string[]; // Array of GUID curriculum group identifiers
}

/**
 * Course bundle extensions
 */
export interface CourseBundleEntity extends CourseEntity {
  readonly courseIds: string[]; // Array of GUID course identifiers in bundle
}
