import { EnrollmentStatusEnum } from '../../enums';
import { CourseEnrollmentCreditsModel } from '../../types';

/**
 * Core enrollment entity interface
 */
export interface EnrollmentEntity {
  // Core identifiers
  readonly id: string; // GUID - enrollment identifier
  readonly courseId: string; // GUID - course identifier
  readonly userId: string; // GUID - learner identifier

  // Enrollment details
  readonly courseName?: string; // course title
  readonly fullName?: string; // learner's full name
  readonly jobTitle?: string; // learner's job title at time of enrollment

  // Progress & performance
  readonly progress: number; // decimal - percentage completed (0-100)
  readonly score: number; // decimal - percentage score (0-100)
  readonly status: EnrollmentStatusEnum;

  // Timing
  readonly dateEnrolled?: string; // ISO 8601 datetime
  readonly dateStarted?: string; // ISO 8601 datetime
  readonly dateCompleted?: string; // ISO 8601 datetime
  readonly dateExpires?: string; // ISO 8601 datetime
  readonly dateDue?: string; // ISO 8601 datetime
  readonly accessDate?: string; // ISO 8601 datetime
  readonly dateEdited: string; // ISO 8601 datetime
  readonly dateAdded: string; // ISO 8601 datetime

  // Time tracking
  readonly timeSpentTicks?: number; // int64 - time in ticks
  readonly timeSpent?: string; // duration string format

  // Course version & keys
  readonly courseVersionId?: string; // GUID - specific course version
  readonly enrollmentKeyId?: string; // GUID - enrollment key used
  readonly certificateId?: string; // GUID - certificate awarded

  // Compliance & tracking
  readonly acceptedTermsAndConditions?: boolean;
  readonly credits?: number; // decimal - credits earned
  readonly isActive: boolean; // current vs historical enrollment

  // Course collection context
  readonly courseCollectionId?: string; // GUID - bundle/curriculum reference

  // User avatar
  readonly avatar?: string; // display path for user avatar

  // Credit breakdown
  readonly creditTypeCredits?: CourseEnrollmentCreditsModel[];
}
