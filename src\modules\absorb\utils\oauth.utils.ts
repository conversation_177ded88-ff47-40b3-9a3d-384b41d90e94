import { BadRequestException } from '@nestjs/common';
import { ConfigService } from '@/config';
import {
  AbsorbOAuthCallbackDto,
  AbsorbOAuthCallbackResponseDto,
  AbsorbOAuthTokenRequestDto,
} from '../dto/oauth.dto';
import { randomBytes } from 'crypto';

/**
 * OAuth utility functions for Absorb LMS integration
 */
export class AbsorbOAuthUtils {
  /**
   * Get base URL from configuration
   * @param configService Configuration service instance
   * @returns Absorb API base URL
   */
  static getBaseUrlFromConfig(configService: ConfigService): string {
    if (!configService.absorb.oauth) {
      throw new BadRequestException('ABSORB_OAUTH_BASE_URL not configured');
    }
    const baseUrl = configService.absorb.oauth.baseUrl;
    if (!baseUrl) {
      throw new BadRequestException('ABSORB_OAUTH_BASE_URL not configured');
    }
    return baseUrl;
  }

  /**
   * Get client ID from configuration
   * @param configService Configuration service instance
   * @returns OAuth client ID
   */
  static getClientIdFromConfig(configService: ConfigService): string {
    if (!configService.absorb.oauth) {
      throw new BadRequestException('ABSORB_OAUTH_CLIENT_ID not configured');
    }
    const clientId = configService.absorb.oauth.clientId;
    if (!clientId) {
      throw new BadRequestException('ABSORB_OAUTH_CLIENT_ID not configured');
    }
    return clientId;
  }

  /**
   * Get client secret from configuration
   * @param configService Configuration service instance
   * @returns OAuth client secret
   */
  static getClientSecretFromConfig(configService: ConfigService): string {
    if (!configService.absorb.oauth) {
      throw new BadRequestException('ABSORB_OAUTH_CLIENT_SECRET not configured');
    }
    const clientSecret = configService.absorb.oauth.clientSecret;
    if (!clientSecret) {
      throw new BadRequestException('ABSORB_OAUTH_CLIENT_SECRET not configured');
    }
    return clientSecret;
  }

  /**
   * Get redirect URI from configuration
   * @param configService Configuration service instance
   * @returns OAuth redirect URI
   */
  static getRedirectUriFromConfig(configService: ConfigService): string {
    if (!configService.absorb.oauth) {
      throw new BadRequestException('ABSORB_OAUTH_REDIRECT_URI not configured');
    }
    const redirectUri = configService.absorb.oauth.redirectUri;
    if (!redirectUri) {
      throw new BadRequestException('ABSORB_OAUTH_REDIRECT_URI not configured');
    }
    return redirectUri;
  }

  /**
   * Generate a cryptographically secure random state parameter
   * @returns Random state string
   */
  static generateSecureState(): string {
    return randomBytes(32).toString('hex');
  }

  /**
   * Generate a cryptographically secure random nonce
   * @returns Random nonce string
   */
  static generateNonce(): string {
    return randomBytes(16).toString('hex');
  }

  /**
   * Create OAuth error response
   * @param errorCode Error code
   * @param errorDescription Optional error description
   * @returns OAuth callback response with error
   */
  static createOAuthErrorResponse(
    errorCode: string,
    errorDescription?: string,
  ): AbsorbOAuthCallbackResponseDto {
    return {
      success: false,
      message: 'OAuth authentication failed',
      error: {
        code: errorCode,
        description: errorDescription ?? 'OAuth authentication failed',
      },
    };
  }

  /**
   * Validate OAuth callback parameters
   * @param callbackParams Callback parameters from OAuth provider
   * @returns Error response if validation fails, null if valid
   */
  static validateCallbackParameters(
    callbackParams: AbsorbOAuthCallbackDto,
  ): AbsorbOAuthCallbackResponseDto | null {
    // Check for error in callback
    if (callbackParams.error) {
      return this.createOAuthErrorResponse(callbackParams.error, callbackParams.error_description);
    }

    // Validate required parameters
    if (!callbackParams.code) {
      return this.createOAuthErrorResponse('invalid_request', 'Authorization code is required');
    }

    if (!callbackParams.state) {
      return this.createOAuthErrorResponse('invalid_request', 'State parameter is required');
    }

    return null;
  }

  /**
   * Create token request object
   * @param code Authorization code
   * @param configService Configuration service instance
   * @returns Token request object
   */
  static createTokenRequest(
    code: string,
    configService: ConfigService,
  ): AbsorbOAuthTokenRequestDto {
    // Get the redirect URI from config
    const redirectUri = this.getRedirectUriFromConfig(configService);

    // Create the token request with the redirect URI
    // The redirect_uri should be the same as the one used in the authorization request
    return {
      grant_type: 'authorization_code',
      code,
      client_id: this.getClientIdFromConfig(configService),
      client_secret: this.getClientSecretFromConfig(configService),
      redirect_uri: redirectUri,
      nonce: this.generateNonce(),
    };
  }
}
