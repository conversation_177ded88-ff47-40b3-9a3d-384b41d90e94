import { IsIn, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, IsString, Matches } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';

/**
 * AbsorbLMS Webhook Headers DTO
 * Validates all required and optional headers from AbsorbLMS webhook requests
 * Based on official AbsorbLMS webhook documentation
 */
export class AbsorbWebhookHeadersDto {
  /**
   * HMAC-SHA256 signature from AbsorbLMS
   * Required for webhook authenticity verification
   */
  @ApiProperty({
    description: 'HMAC-SHA256 signature for webhook verification',
    example: 'af13ce1932ef954bfcf795a061fba1eb52ece035d01d0564fe0457750f685c92',
    required: true,
  })
  @IsNotEmpty({ message: 'absorb-signature header is required' })
  @IsString({ message: 'absorb-signature must be a string' })
  @Matches(/^[a-f0-9]{64}$/, {
    message: 'absorb-signature must be a valid 64-character hexadecimal string',
  })
  @Transform(({ value }: { value: unknown }) =>
    typeof value === 'string' ? value.toLowerCase() : value,
  )
  absorbSignature: string;

  /**
   * Timestamp property for easier access
   * Converted from xAbsorbTimestamp
   */
  get timestamp(): number | undefined {
    return this.xAbsorbTimestamp ? parseInt(this.xAbsorbTimestamp, 10) : undefined;
  }

  /**
   * User-Agent header for request identification
   * Helps with authentication and monitoring
   */
  @ApiProperty({
    description: 'User-Agent header from AbsorbLMS',
    example: 'AbsorbLMS-Webhook/1.0',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'user-agent must be a string' })
  userAgent?: string | undefined;

  /**
   * Content-Type header
   * Should always be application/json for AbsorbLMS webhooks
   */
  @ApiProperty({
    description: 'Content-Type header',
    example: 'application/json; charset=UTF-8',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'content-type must be a string' })
  @IsIn(['application/json', 'application/json; charset=UTF-8'], {
    message: 'content-type must be application/json or application/json; charset=UTF-8',
  })
  contentType?: string | undefined;

  /**
   * Content-Length header
   * Indicates the size of the request body
   */
  @ApiProperty({
    description: 'Content-Length header',
    example: '168',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'content-length must be a string' })
  @Matches(/^\d+$/, { message: 'content-length must be a numeric string' })
  contentLength?: string | undefined;

  /**
   * Host header
   * The target host for the webhook
   */
  @ApiProperty({
    description: 'Host header',
    example: 'your-webhook-endpoint.com',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'host must be a string' })
  host?: string | undefined;

  /**
   * Accept-Encoding header
   * Compression methods accepted by the client
   */
  @ApiProperty({
    description: 'Accept-Encoding header',
    example: 'gzip, deflate',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'accept-encoding must be a string' })
  acceptEncoding?: string | undefined;

  /**
   * Trace parent for distributed tracing
   * Used for request correlation across systems
   */
  @ApiProperty({
    description: 'Traceparent header for distributed tracing',
    example: '00-21d5220ddd374cdd1094be948e8ace0a-87d3ef234489bba7-00',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'traceparent must be a string' })
  traceparent?: string | undefined;

  /**
   * Trace state for distributed tracing
   * Additional tracing information
   */
  @ApiProperty({
    description: 'Tracestate header for distributed tracing',
    example:
      '4393968@nr=0-0-3095660-192816082-0a1f5d46fa4e7702-9ba89a6dc4bc8eaa-0-0.951582-1757203923796',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'tracestate must be a string' })
  tracestate?: string | undefined;

  /**
   * X-Forwarded-For header
   * Original client IP when behind a proxy
   */
  @ApiProperty({
    description: 'X-Forwarded-For header',
    example: '************',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'x-forwarded-for must be a string' })
  xForwardedFor?: string | undefined;

  /**
   * X-Forwarded-Host header
   * Original host when behind a proxy
   */
  @ApiProperty({
    description: 'X-Forwarded-Host header',
    example: 'your-webhook-endpoint.com',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'x-forwarded-host must be a string' })
  xForwardedHost?: string | undefined;

  /**
   * X-Forwarded-Proto header
   * Original protocol when behind a proxy
   */
  @ApiProperty({
    description: 'X-Forwarded-Proto header',
    example: 'https',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'x-forwarded-proto must be a string' })
  @IsIn(['http', 'https'], {
    message: 'x-forwarded-proto must be either http or https',
  })
  xForwardedProto?: string | undefined;

  /**
   * Custom timestamp header for replay attack prevention
   * Can be used to validate request freshness
   */
  @ApiProperty({
    description: 'Timestamp header for replay attack prevention',
    example: '1704067200',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'x-absorb-timestamp must be a string' })
  @Matches(/^\d{10}$/, {
    message: 'x-absorb-timestamp must be a 10-digit Unix timestamp',
  })
  xAbsorbTimestamp?: string | undefined;
}

/**
 * Webhook Headers Validation Result
 * Used for validation response and error handling
 */
export class WebhookHeadersValidationResult {
  @ApiProperty({
    description: 'Whether headers validation passed',
    example: true,
  })
  isValid: boolean;

  @ApiProperty({
    description: 'Validation error messages',
    example: ['absorb-signature header is required'],
    required: false,
  })
  errors?: string[];

  @ApiProperty({
    description: 'Validated headers object',
    required: false,
  })
  headers?: AbsorbWebhookHeadersDto;
}

/**
 * Webhook Security Context
 * Contains security-related information extracted from headers
 */
export class WebhookSecurityContext {
  @ApiProperty({
    description: 'HMAC signature for verification',
    example: 'af13ce1932ef954bfcf795a061fba1eb52ece035d01d0564fe0457750f685c92',
  })
  signature: string;

  @ApiProperty({
    description: 'Request timestamp for replay protection',
    example: 1704067200,
    required: false,
  })
  timestamp?: number;

  @ApiProperty({
    description: 'Client IP address',
    example: '************',
    required: false,
  })
  clientIp?: string;

  @ApiProperty({
    description: 'User agent string',
    example: 'AbsorbLMS-Webhook/1.0',
    required: false,
  })
  userAgent?: string;

  @ApiProperty({
    description: 'Trace correlation ID',
    example: '21d5220ddd374cdd1094be948e8ace0a',
    required: false,
  })
  traceId?: string;

  @ApiProperty({
    description: 'Content type of the request',
    example: 'application/json',
    required: false,
  })
  contentType?: string;
}
