import { AbsorbRawWebhookPayload } from './absorb-raw-webhook-payload.interface';
import { AbsorbWebhookRequest } from './absorb-webhook-request.interface';
import { AbsorbWebhookSignatureVerification } from './absorb-webhook-signature-verification.interface';
import { AbsorbBaseWebhookPayload } from './absorb-base-webhook-payload.interface';

/**
 * Your enhanced processing context
 * Combines raw Absorb data with your standardized format
 */
export interface AbsorbWebhookProcessingContext {
  /** Unique correlation ID for request tracking */
  readonly correlationId: string;

  /** Processing start time for performance tracking */
  readonly startTime: number;

  /** Raw webhook request from Absorb */
  readonly rawRequest: AbsorbWebhookRequest;

  /** Raw payload as received from Absorb */
  readonly rawPayload: AbsorbRawWebhookPayload;

  /** Your standardized, parsed payload */
  readonly payload: AbsorbBaseWebhookPayload;

  /** Signature verification result */
  readonly signatureVerification: AbsorbWebhookSignatureVerification;

  /** Processing metadata */
  readonly metadata?: {
    /** Environment (dev, staging, prod) */
    readonly environment?: string;

    /** Service version */
    readonly version?: string;

    /** Additional processing context */
    readonly [key: string]: unknown;
  };
}
