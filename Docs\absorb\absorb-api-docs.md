# Absorb LMS Integration API Documentation

## Table of Contents

1. [Introduction](#introduction)
2. [Getting Started](#getting-started)
3. [Authentication](#authentication)
4. [API Key Configuration](#api-key-configuration)
5. [Request Configuration](#request-configuration)
6. [Pagination](#pagination)
7. [Sorting](#sorting)
8. [Filtering](#filtering)
9. [API Versioning](#api-versioning)
10. [Rate Limiting](#rate-limiting)
11. [Best Practices](#best-practices)
12. [Recent Updates](#recent-updates)
13. [Integration Options](#integration-options)
14. [Support](#support)

---

## Introduction

The Absorb LMS Integration API is a RESTful API that provides developers with comprehensive tools to integrate seamlessly with the Learning Management System. The API enables you to access, create, update, and manage resources effortlessly, allowing customized interactions with your LMS data including:

- **Learners and Users**
- **Courses and Enrollments**
- **Departments and Groups**
- **Grades and Assessments**
- **Learning Paths**
- **Competencies**
- **Custom Reports**
- **Manager Experience**

### Key Features

- Service gateway-based RESTful architecture
- Multiple version support for backward compatibility
- Robust rate limiting and error handling
- Support for complex filtering and sorting
- Real-time webhook integration capabilities
- Region-specific endpoints for optimal performance

---

## Getting Started

### Prerequisites

1. **API Private Key**: Contact Absorb to purchase an API subscription
2. **Admin Credentials**: Your LMS Admin role determines API access permissions
3. **Technical Experience**: Basic understanding of REST APIs and HTTP methods

### Service Gateway URLs

The Integration API uses region-specific service gateways. All endpoints must be prefixed with the appropriate gateway URL:

**Production Environments:**

- North America: `https://[your-portal].myabsorb.com/api/rest/v2/`
- Europe: `https://[your-portal].myabsorb.eu/api/rest/v2/`
- Other regions: Contact support for specific endpoints

**Sandbox Environments:**

- Append `/sandbox` to your production URL
- Note: Sandbox environments must be explicitly created

### API Documentation Access

System Admins can access the full API documentation at: https://docs.myabsorb.com/

---

## Authentication

The Absorb Integration API uses a two-step authentication process:

### Step 1: Generate API Token

Generate an authentication token using your private key and admin credentials.

**Endpoint:** `POST /authenticate`

**Headers:**

```json
{
  "x-api-key": "your-private-api-key",
  "Content-Type": "application/json"
}
```

**Request Body:**

```json
{
  "username": "admin_username",
  "password": "admin_password",
  "portal": "your_portal_name"
}
```

### Step 2: Use Token in Requests

Include the generated token in all subsequent API requests.

**Headers for API Requests:**

```json
{
  "Authorization": "Bearer your-api-token",
  "x-api-key": "your-private-api-key",
  "Content-Type": "application/json",
  "x-api-version": "2" // Optional but recommended
}
```

### Token Lifecycle

- **Validity**: 4 hours from generation
- **Invalidation**: When the same username requests a new token
- **Best Practice**: Implement token refresh logic before expiration

---

## API Key Configuration

### Obtaining Your Private Key

1. Navigate to your portal settings in the Absorb Admin interface
2. Access the API configuration section
3. Copy your private API key
4. Store securely - this key provides full API access

### Security Requirements

- **Required Header**: `x-api-key` must be included in all requests
- **401 Unauthorized**: Returned when accessing without a valid key
- **Key Rotation**: Contact support for key rotation procedures

---

## Request Configuration

### HTTP Methods Supported

- **GET**: Retrieve resources
- **POST**: Create new resources
- **PUT**: Update existing resources (full replacement)
- **PATCH**: Partial updates
- **DELETE**: Remove resources

### Request Structure Example

**Creating a New User:**

```http
POST /users
Headers:
  Authorization: Bearer your-token
  x-api-key: your-private-key
  Content-Type: application/json
  x-api-version: 2

Body:
{
  "username": "john.doe",
  "emailAddress": "<EMAIL>",
  "firstName": "John",
  "lastName": "Doe",
  "departmentId": "dept-123",
  "isActive": true,
  "customFields": {
    "employeeId": "EMP001",
    "jobTitle": "Manager"
  }
}
```

---

## Pagination

### Default Settings

- **Default Offset**: 0 (first page)
- **Default Limit**: 20 records
- **Maximum Limit**: 1000 records per request
- **Maximum Offset**: 65535

### Query Parameters

```
GET /users?_offset=0&_limit=100
```

### Pagination Strategy

```javascript
// Example pagination loop
let offset = 0;
const limit = 100;
let hasMore = true;

while (hasMore) {
  const response = await fetch(`/users?_offset=${offset}&_limit=${limit}`);
  const data = await response.json();

  if (data.length < limit) {
    hasMore = false;
  }

  processRecords(data);
  offset++;
}
```

### Best Practices for Pagination

1. Use consistent limit values across requests
2. Implement retry logic for failed pages
3. Consider total record count for progress tracking
4. Cache results when appropriate

---

## Sorting

### Basic Sorting

```
GET /users?_sort=lastName
```

### Descending Order

Prefix the field with `-`:

```
GET /users?_sort=-dateAdded
```

### Multiple Sort Fields

Use comma-separated values:

```
GET /users?_sort=departmentName,-lastName,firstName
```

### Sorting Nested Properties

For custom fields and nested objects:

```
GET /users?_sort=customFields/employeeId
GET /users?_sort=-customFields/hireDate
```

---

## Filtering

### Supported Operators

Based on OData filter syntax:

| Operator | Description           | Example                                              |
| -------- | --------------------- | ---------------------------------------------------- |
| `eq`     | Equals                | `status eq 'active'`                                 |
| `ne`     | Not equals            | `status ne 'inactive'`                               |
| `gt`     | Greater than          | `score gt 80`                                        |
| `ge`     | Greater than or equal | `dateAdded ge datetime'2024-01-01T00:00:00Z'`        |
| `lt`     | Less than             | `attempts lt 3`                                      |
| `le`     | Less than or equal    | `progress le 50`                                     |
| `and`    | Logical AND           | `status eq 'active' and score gt 80`                 |
| `or`     | Logical OR            | `department eq 'Sales' or department eq 'Marketing'` |
| `not`    | Logical NOT           | `not(status eq 'completed')`                         |

### String Functions

- `substringof(needle, haystack)`: Check if string contains substring
- `startswith(string, prefix)`: Check if string starts with prefix
- `endswith(string, suffix)`: Check if string ends with suffix
- `tolower(string)`: Convert to lowercase
- `toupper(string)`: Convert to uppercase

### Filtering Examples

**Basic Filter:**

```
GET /users?_filter=emailAddress eq '<EMAIL>'
```

**Complex Filter:**

```
GET /users?_filter=startsWith(lastName, 'Sm') and dateAdded ge datetime'2024-01-01T00:00:00Z'
```

**Nested Property Filter:**

```
GET /users?_filter=customFields/department eq 'Engineering' and customFields/level gt 3
```

### Limitations

- Blank strings ('') and null values not supported in nested property filters
- DateTime values must be in ISO 8601 format

---

## API Versioning

### Version Header

Include the version header in your requests:

```
x-api-version: 2
```

### Available Versions

- **Version 1**: Legacy support (restricted features)
- **Version 2**: Current version with full feature set

### Version Selection

```http
GET /users
Headers:
  x-api-version: 2
```

### Migration Strategy

1. Test new version in sandbox environment
2. Update integration code gradually
3. Monitor for deprecated features
4. Complete migration before version sunset

---

## Rate Limiting

### Current Limits

- **Requests per second**: 200
- **Burst capacity**: Additional 100 requests
- **Response code**: `429 Too Many Requests` when exceeded

### Handling Rate Limits

**Exponential Backoff Implementation:**

```javascript
async function makeRequestWithRetry(url, options, maxRetries = 5) {
  for (let i = 0; i < maxRetries; i++) {
    try {
      const response = await fetch(url, options);

      if (response.status === 429) {
        const delay = Math.pow(2, i) * 1000 + Math.random() * 1000;
        await new Promise(resolve => setTimeout(resolve, delay));
        continue;
      }

      return response;
    } catch (error) {
      if (i === maxRetries - 1) throw error;
    }
  }
}
```

### Best Practices

1. Implement request queuing
2. Use exponential backoff with jitter
3. Monitor 429 responses
4. Consider batch operations where available
5. Implement global rate limiting in your application

---

## Best Practices

### Dynamic Rules Processing

- Allow up to 10 minutes for dynamic rule processing
- Automatic enrollments based on user attributes may have delays
- Batch updates are processed more efficiently

### Data Consistency

1. **Validate incoming data** against expected formats
2. **Implement idempotency** for critical operations
3. **Regular audits** of synchronized data
4. **Cross-reference** critical data points
5. **Handle eventual consistency** in dynamic rules

### Error Handling

- Always include correlation IDs in support requests
- Log both `X-Absorb-Correlation-Id` and `x-amzn-RequestId` headers
- Implement comprehensive error logging
- Use structured error responses

### File Operations

- **Not Supported**: Direct file uploads via API
- **Alternative**: Use the Absorb UI for file operations
- **Workaround**: Consider base64 encoding for small files in custom fields

---

## Recent Updates

### Version 5.121.0 Updates

**Course Evaluation Endpoints (v2 only):**

- `GET /courses/{courseId}/evaluation-answers` - Retrieve evaluation answers
- `GET /courses/{courseId}/evaluation-questions` - List evaluation questions
- `GET /courses/{courseId}/evaluation-questions/{questionId}` - Get specific question

### Version 5.119.0 Updates

**Manager Experience Enhancements:**

- `GET /managers/{managerId}/direct-reports` - List manager's direct reports
- `POST /managers/{managerId}/direct-reports` - Update direct reports
- Added `IsManager` property to user endpoints

### Version 5.118.0 Updates

**Learning Path and Assessment Features:**

- `GET /learning-path/{userId}` - Get user's learning path
- `GET /self-assessments/{selfAssessmentId}/skill-and-competency-ratings` - Get assessment ratings

### Version 5.117.1 Updates

**Competency Management:**

- `GET /competency-definition/{id}` - Retrieve competency definitions including badge images

---

## Integration Options

### Pre-built Integrations

#### HR Systems

- **Workday Connector**: Sync worker profiles and department structures
- **SAP SuccessFactors**: Align learning with talent management
- **ADP**: Streamline HR and learning processes

#### CRM and Sales

- **Salesforce**: Integrate training with customer relationship management
- **Zendesk Sell**: Enhance sales automation with training programs

#### IT Service Management

- **ServiceNow**: Connect IT service delivery with training initiatives

#### Communication Platforms

- **Microsoft Teams**: Enable collaborative learning
- **Zoom**: Integrate virtual training sessions
- **Slack**: Send training notifications and updates

### Single Sign-On (SSO)

- **SAML 2.0**: Full support for SAML-based authentication
- **LDAPS**: OpenID Connect/OAuth support
- **Microsoft Entra ID**: Native integration support

### Content Libraries

- **Absorb Amplify**: Three-tier content portfolio
- **LinkedIn Learning**: Direct content integration
- **GO1**: Extensive learning content library
- **Traliant**: Compliance training content

### Webhooks

Real-time event notifications for:

- Course completions
- User registrations
- Content updates
- Enrollment changes
- Assessment completions

**Webhook Configuration Example:**

```json
{
  "url": "https://your-endpoint.com/webhook",
  "events": ["course.completed", "user.created", "enrollment.updated"],
  "secret": "your-webhook-secret"
}
```

---

## Support

### Getting Help

- **Documentation**: https://docs.myabsorb.com/
- **Support Portal**: https://support.anthropic.com
- **API-specific queries**: Include correlation IDs from response headers

### When Contacting Support

Always provide:

1. `X-Absorb-Correlation-Id` header value
2. `x-amzn-RequestId` header value
3. Request timestamp
4. Request method and endpoint
5. Response status code
6. Error message (if applicable)

### Additional Resources

- **Ideas Portal**: https://ideas.absorblms.com/
- **Release Notes**: Check the changelog at https://docs.myabsorb.com/changelog
- **API Glossary**: Understanding of REST API terminology
- **Professional Services**: Available for custom integration development

---

## Appendix: Common Use Cases

### User Management

```javascript
// Create user with custom fields
const createUser = async userData => {
  const response = await fetch('/users', {
    method: 'POST',
    headers: {
      Authorization: `Bearer ${token}`,
      'x-api-key': apiKey,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      username: userData.email,
      emailAddress: userData.email,
      firstName: userData.firstName,
      lastName: userData.lastName,
      customFields: {
        employeeId: userData.employeeId,
        department: userData.department,
      },
    }),
  });
  return response.json();
};
```

### Course Enrollment

```javascript
// Enroll user in course
const enrollUser = async (userId, courseId) => {
  const response = await fetch('/enrollments', {
    method: 'POST',
    headers: {
      Authorization: `Bearer ${token}`,
      'x-api-key': apiKey,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      userId: userId,
      courseId: courseId,
      dateEnrolled: new Date().toISOString(),
    }),
  });
  return response.json();
};
```

### Bulk Operations

```javascript
// Batch update users
const batchUpdateUsers = async users => {
  const results = [];

  for (const batch of chunk(users, 50)) {
    const promises = batch.map(user => updateUser(user.id, user.data));

    const batchResults = await Promise.allSettled(promises);
    results.push(...batchResults);

    // Respect rate limits
    await new Promise(resolve => setTimeout(resolve, 1000));
  }

  return results;
};
```

---

_This documentation is based on the Absorb LMS Integration API v2. For the most current information, please refer to the official documentation at https://docs.myabsorb.com/_
