import { WebhookEventType } from '@/modules/absorb/enums';
import { AbsorbBaseWebhookPayload } from '../absorb-base-webhook-payload.interface';

/**
 * Survey completion payload
 */
export interface SurveyCompletedPayload extends AbsorbBaseWebhookPayload {
  readonly eventType: WebhookEventType.SURVEY_COMPLETED;
  readonly surveyId: string; // GUID - survey identifier
  readonly responses?: number; // int32 - number of responses
  readonly dateCompleted: string; // ISO 8601 datetime
}
