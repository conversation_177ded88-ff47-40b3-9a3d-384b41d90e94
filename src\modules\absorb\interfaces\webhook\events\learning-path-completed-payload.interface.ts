import { WebhookEventType } from '@/modules/absorb/enums';
import { AbsorbBaseWebhookPayload } from '../absorb-base-webhook-payload.interface';
/**
 * Learning path completion payload
 */
export interface LearningPathCompletedPayload extends AbsorbBaseWebhookPayload {
  readonly eventType: WebhookEventType.LEARNING_PATH_COMPLETED;
  readonly learningPathId: string; // GUID - learning path identifier
  readonly completionRate: number; // decimal percentage
}
