import { Global, Module } from '@nestjs/common';
import { ConfigModule as NestConfigModule } from '@nestjs/config';
import { ConfigService } from './config.service';
import { configValidationSchema } from './config.validation';

/**
 * Configuration module that sets up environment-based configuration
 * This module is global and can be imported anywhere in the application
 */
@Global()
@Module({
  imports: [
    NestConfigModule.forRoot({
      // Load environment files based on NODE_ENV
      envFilePath: [
        '.env.local', // Local overrides (not committed)
        `.env.${process.env['NODE_ENV'] ?? 'development'}`, // Environment specific
        '.env', // Default fallback
      ],
      // Validate environment variables using Joi schema
      validationSchema: configValidationSchema,
      // Validation options
      validationOptions: {
        allowUnknown: true, // Allow unknown environment variables
        abortEarly: false, // Show all validation errors
      },
      // Make configuration global
      isGlobal: true,
      // Cache configuration for better performance
      cache: true,
      // Expand variables in .env files (e.g., ${VAR_NAME})
      expandVariables: true,
    }),
  ],
  providers: [ConfigService],
  exports: [ConfigService],
})
export class ConfigModule {}
