import { Module } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import { ConfigModule } from '@nestjs/config';
import { AbsorbController } from './controllers/absorb.controller';
import { AbsorbAuthController } from './controllers/absorb-auth.controller';
import { AbsorbWebhookController } from './controllers/absorb-webhook.controller';
import { AbsorbOAuthController } from './controllers/absorb-oauth.controller';
import { AbsorbService } from './services/absorb.service';
import { AbsorbAuthService } from './services/absorb-auth.service';
import { AbsorbOAuthService } from './services/absorb-oauth.service';
import { AbsorbWebhookService } from './services/webhook/absorb-webhook.service';
import { AbsorbWebhookProcessingService } from './services/webhook/absorb-webhook-processing.service';
import { AbsorbIntegrationApiService } from './services/absorb-integration-api.service';
import { WebhookSignatureVerificationService } from './services/webhook/webhook-signature-verification.service';
import { AbsorbConfigService } from './config/absorb-config.service';
import { WebhookHeadersValidationGuard } from './guards/webhook-headers-validation.guard';
import { AbsorbWebhookEventHandlersService } from './services/webhook';

/**
 * Absorb module
 * This module provides comprehensive Absorb LMS integration functionality
 * Includes authentication, OAuth 2.0 support, webhook processing, and event handling
 *
 * Features:
 * - REST API integration with Absorb LMS
 * - OAuth 2.0 authentication flow
 * - Webhook signature verification and event processing
 * - Integration API support for data enrichment
 * - Comprehensive event handling for Administrator and Learner events
 */
@Module({
  imports: [
    HttpModule.register({
      timeout: 30000,
      maxRedirects: 5,
    }),
    ConfigModule,
  ],
  controllers: [
    AbsorbController,
    AbsorbAuthController,
    AbsorbOAuthController,
    AbsorbWebhookController,
  ],
  providers: [
    AbsorbService,
    AbsorbAuthService,
    AbsorbOAuthService,
    AbsorbWebhookService,
    AbsorbWebhookEventHandlersService,
    AbsorbWebhookProcessingService,
    AbsorbIntegrationApiService,
    WebhookSignatureVerificationService,
    AbsorbConfigService,
    WebhookHeadersValidationGuard,
  ],
  exports: [
    AbsorbService,
    AbsorbAuthService,
    AbsorbOAuthService,
    AbsorbWebhookService,
    AbsorbWebhookEventHandlersService,
    AbsorbWebhookProcessingService,
    AbsorbIntegrationApiService,
    WebhookSignatureVerificationService,
    AbsorbConfigService,
    WebhookHeadersValidationGuard,
  ],
})
export class AbsorbModule {}
