import { WebhookEventType } from '@/modules/absorb/enums';
import { AbsorbBaseWebhookPayload } from '../absorb-base-webhook-payload.interface';

/**
 * Department management payloads
 */
export interface DepartmentCreatedPayload extends AbsorbBaseWebhookPayload {
  readonly eventType: WebhookEventType.DEPARTMENT_CREATED;
  readonly departmentName: string;
  readonly parentDepartmentId?: string; // GUID
}
