import * as joi from 'joi';

/**
 * Joi validation schema for environment variables
 */
export const configValidationSchema = joi.object({
  // Application configuration
  NODE_ENV: joi
    .string()
    .valid('development', 'staging', 'production', 'test')
    .default('development'),
  PORT: joi.number().port().default(3000),
  API_PREFIX: joi.string().default('api/v1'),

  // Database configuration
  DATABASE_URL: joi.string().uri().optional(),
  DATABASE_HOST: joi.string().default('localhost'),
  DATABASE_PORT: joi.number().port().default(5432),
  DATABASE_NAME: joi.string().required(),
  DATABASE_USERNAME: joi.string().required(),
  DATABASE_PASSWORD: joi.string().required(),

  // JWT configuration
  JWT_SECRET: joi.string().min(32).required(),
  JWT_EXPIRES_IN: joi.string().default('1d'),

  // CORS configuration
  CORS_ORIGIN: joi.string().default('http://localhost:3000'),

  // Logging configuration
  LOG_LEVEL: joi.string().valid('error', 'warn', 'info', 'debug', 'verbose').default('info'),

  // Rate limiting configuration
  THROTTLE_TTL: joi.number().positive().default(60),
  THROTTLE_LIMIT: joi.number().positive().default(10),

  // Feature flags
  ENABLE_SWAGGER: joi.boolean().default(true),
  ENABLE_DEBUG: joi.boolean().default(false),

  // Sentry configuration
  SENTRY_DSN: joi.string().uri().optional(),
  SENTRY_ENVIRONMENT: joi.string().default('development'),
  SENTRY_ENABLED: joi.boolean().default(false),
  SENTRY_TRACES_SAMPLE_RATE: joi.number().min(0).max(1).default(0.1),
  SENTRY_PROFILES_SAMPLE_RATE: joi.number().min(0).max(1).default(0.1),
  SENTRY_ENABLE_LOGS: joi.boolean().default(false),

  // Absorb LMS configuration
  ABSORB_API_BASE_URL: joi.string().uri().default('https://rest.myabsorb.com.au'),
  ABSORB_API_KEY: joi.string().optional(),
  ABSORB_API_VERSION: joi.string().default('v2'),
  ABSORB_PRIVATE_KEY: joi.string().optional(),
  ABSORB_API_TIMEOUT: joi.number().positive().default(30000),
  ABSORB_RETRY_ATTEMPTS: joi.number().min(0).max(10).default(3),
  ABSORB_RETRY_DELAY: joi.number().positive().default(1000),

  //Auth
  ABSORB_USERNAME: joi.string().optional(),
  ABSORB_PASSWORD: joi.string().optional(),

  // Absorb OAuth configuration
  ABSORB_OAUTH_BASE_URL: joi.string().uri().optional(),
  ABSORB_OAUTH_CLIENT_ID: joi.string().optional(),
  ABSORB_OAUTH_CLIENT_SECRET: joi.string().optional(),
  ABSORB_OAUTH_REDIRECT_URI: joi.string().uri().optional(),
});
