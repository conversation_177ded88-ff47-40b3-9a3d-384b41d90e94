import { AbsorbWebhookHeaders } from './absorb-webhook-headers.interface';

/**
 * Raw webhook request as received from Absorb
 */
export interface AbsorbWebhookRequest {
  /** HTTP method (typically POST) */
  readonly method: string;

  /** Request URL */
  readonly url: string;

  /** Client IP address */
  readonly ip: string;

  /** Raw request body as string */
  readonly body: string;

  /** Request headers */
  readonly headers: AbsorbWebhookHeaders;
}
