import {
  type ArgumentsHost,
  Catch,
  ExceptionFilter,
  HttpException,
  Injectable,
  Logger,
} from '@nestjs/common';
import { SentryExceptionCaptured } from '@sentry/nestjs';
import * as sentry from '@sentry/nestjs';
import type { Request, Response } from 'express';

/**
 * Custom Sentry exception filter that only captures serious errors
 * This filter ensures that only server errors (5xx) and unexpected exceptions
 * are sent to Sentry, while client errors (4xx) are handled locally
 */
@Injectable()
@Catch()
export class SentryExceptionFilter implements ExceptionFilter {
  private readonly logger = new Logger(SentryExceptionFilter.name);

  @SentryExceptionCaptured()
  catch(exception: unknown, host: ArgumentsHost): void {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<Request>();

    const { status, shouldCaptureInSentry } = this._analyzeException(exception);

    if (shouldCaptureInSentry) {
      this._captureInSentry(exception, request, status);
    }

    this._logException(exception, request, status, shouldCaptureInSentry);
    this._sendErrorResponse(response, status, exception);
  }

  private _analyzeException(exception: unknown): {
    status: number;
    shouldCaptureInSentry: boolean;
  } {
    let status = 500; // Use numeric value instead of enum
    let shouldCaptureInSentry = true;

    // Handle HTTP exceptions
    if (exception instanceof HttpException) {
      status = exception.getStatus();

      // Only capture server errors (5xx) in Sentry, not client errors (4xx)
      shouldCaptureInSentry = status >= 500;
    }

    // Handle validation errors and other expected exceptions
    if (exception instanceof Error) {
      const errorName = exception.constructor.name;

      // Don't capture validation errors, bad request errors, etc.
      if (
        errorName === 'ValidationError' ||
        errorName === 'BadRequestException' ||
        errorName === 'UnauthorizedException' ||
        errorName === 'ForbiddenException' ||
        errorName === 'NotFoundException' ||
        errorName === 'ConflictException' ||
        errorName === 'UnprocessableEntityException'
      ) {
        shouldCaptureInSentry = false;
      }
    }

    return { status, shouldCaptureInSentry };
  }

  private _captureInSentry(exception: unknown, request: Request, status: number): void {
    sentry.withScope(scope => {
      scope.setTag('component', 'exception-filter');
      scope.setContext('request', {
        method: request.method,
        url: request.url,
        userAgent: request.get('user-agent'),
        ip: request.ip,
      });
      scope.setContext('response', {
        statusCode: status,
      });
      scope.setLevel('error');

      sentry.captureException(exception);
    });
  }

  private _logException(
    exception: unknown,
    request: Request,
    status: number,
    shouldCaptureInSentry: boolean,
  ): void {
    const message = exception instanceof Error ? exception.message : 'Unknown error';
    const logMessage = `${request.method} ${request.url} - ${status} - ${message}`;

    if (shouldCaptureInSentry) {
      this.logger.error(logMessage, exception instanceof Error ? exception.stack : exception);
    } else {
      this.logger.warn(logMessage);
    }
  }

  private _sendErrorResponse(response: Response, status: number, exception: unknown): void {
    const message = exception instanceof Error ? exception.message : 'Internal server error';

    const errorResponse = {
      statusCode: status,
      timestamp: new Date().toISOString(),
      path: response.req.url,
      method: response.req.method,
      message: status >= 500 ? 'Internal server error' : message,
    };

    response.status(status).json(errorResponse);
  }

  /**
   * Sanitize request headers to remove sensitive information
   */
  private _sanitizeHeaders(headers: Record<string, unknown>): Record<string, unknown> {
    const sanitized = { ...headers };

    // Remove sensitive headers
    const sensitiveHeaders = [
      'authorization',
      'cookie',
      'x-api-key',
      'x-auth-token',
      'x-access-token',
    ];

    sensitiveHeaders.forEach(header => {
      if (sanitized[header]) {
        sanitized[header] = '[REDACTED]';
      }
    });

    return sanitized;
  }
}
