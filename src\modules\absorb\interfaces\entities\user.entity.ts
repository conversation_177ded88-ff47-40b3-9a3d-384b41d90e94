import { ActiveStatusEnum, GenderEnum, UserManagementTypeEnum } from '../../enums';
import { DepartmentSelectionDefinition, UserType } from '../../types';

/**
 * User custom fields structure
 */
export interface UserCustomFields {
  // Decimal fields (5 available)
  readonly decimal1?: number;
  readonly decimal2?: number;
  readonly decimal3?: number;
  readonly decimal4?: number;
  readonly decimal5?: number;

  // String fields (30 available)
  readonly string1?: string;
  readonly string2?: string;
  readonly string3?: string;
  readonly string4?: string;
  readonly string5?: string;
  readonly string6?: string;
  readonly string7?: string;
  readonly string8?: string;
  readonly string9?: string;
  readonly string10?: string;
  readonly string11?: string;
  readonly string12?: string;
  readonly string13?: string;
  readonly string14?: string;
  readonly string15?: string;
  readonly string16?: string;
  readonly string17?: string;
  readonly string18?: string;
  readonly string19?: string;
  readonly string20?: string;
  readonly string21?: string;
  readonly string22?: string;
  readonly string23?: string;
  readonly string24?: string;
  readonly string25?: string;
  readonly string26?: string;
  readonly string27?: string;
  readonly string28?: string;
  readonly string29?: string;
  readonly string30?: string;

  // DateTime fields (5 available)
  readonly datetime1?: string; // ISO 8601 datetime
  readonly datetime2?: string;
  readonly datetime3?: string;
  readonly datetime4?: string;
  readonly datetime5?: string;

  // Boolean fields (5 available)
  readonly bool1?: boolean;
  readonly bool2?: boolean;
  readonly bool3?: boolean;
  readonly bool4?: boolean;
  readonly bool5?: boolean;
}

/**
 * User management settings
 */
export interface UserManagementSettings {
  readonly userTypes: UserType[];
  readonly userManagementType: UserManagementTypeEnum;
  readonly managedGroupId?: string; // GUID - when managing specific group
  readonly managedDepartments?: DepartmentSelectionDefinition[];
}

/**
 * Core user entity interface
 */
export interface UserEntity {
  // Required fields
  readonly id: string; // GUID - unique user identifier
  readonly departmentId: string; // GUID - required, user's department
  readonly firstName: string; // max 255 chars - required
  readonly lastName: string; // max 255 chars - required
  readonly username: string; // max 255 chars - required, must be unique

  // Optional identity fields
  readonly middleName?: string; // max 255 chars
  readonly emailAddress?: string; // max 255 chars with email validation
  readonly externalId?: string; // max 255 chars - external system integration

  // Contact information
  readonly ccEmailAddresses?: string[]; // max 5 addresses
  readonly phone?: string; // max 255 chars
  readonly address?: string; // max 4000 chars
  readonly address2?: string; // max 4000 chars
  readonly city?: string; // max 255 chars
  readonly postalCode?: string; // max 255 chars
  readonly provinceId?: string; // GUID
  readonly countryId?: string; // GUID

  // Employment information
  readonly employeeNumber?: string; // max 255 chars
  readonly jobTitle?: string; // max 255 chars
  readonly location?: string; // max 255 chars
  readonly referenceNumber?: string; // max 255 chars
  readonly dateHired?: string; // ISO 8601 datetime
  readonly dateTerminated?: string; // ISO 8601 datetime
  readonly supervisorId?: string; // GUID

  // System fields
  readonly languageId?: number; // int32
  readonly gender?: GenderEnum;
  readonly activeStatus: ActiveStatusEnum;
  readonly notes?: string; // free text

  // Audit fields (read-only)
  readonly dateEdited: string; // ISO 8601 datetime
  readonly dateAdded: string; // ISO 8601 datetime
  readonly lastLoginDate?: string; // ISO 8601 datetime

  // Role and type flags
  readonly roleIds?: string[]; // Array of GUID role identifiers
  readonly isLearner: boolean;
  readonly isAdmin: boolean;
  readonly isInstructor: boolean;
  readonly isManager: boolean;
  readonly hasUsername: boolean;

  // Custom fields
  readonly customFields?: UserCustomFields;
  readonly managementSettings?: UserManagementSettings;
}
