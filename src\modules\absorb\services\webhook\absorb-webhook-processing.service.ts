import { Injectable, Logger } from '@nestjs/common';
import { Request, Response } from 'express';
import { AbsorbWebhookService } from './absorb-webhook.service';
import { WebhookSignatureVerificationService } from './webhook-signature-verification.service';
import { AbsorbWebhookResponse } from '../../interfaces/webhook/absorb-webhook-response.interface';
import { AbsorbWebhookHeadersDto } from '../../dto/webhook-headers.dto';
import { AbsorbConfigService } from '../../config/absorb-config.service';
import { v4 as uuidv4 } from 'uuid';

/**
 * Extended Request interface to include validated headers from the guard
 */
interface RequestWithValidatedHeaders extends Request {
  validatedHeaders?: AbsorbWebhookHeadersDto;
}

/**
 * Service responsible for processing webhook requests and coordinating
 * between security validation and business logic execution.
 *
 * @param webhookService - Service to handle business logic for webhooks
 * @param signatureVerificationService - Service to verify webhook signatures
 * @param configService - Service to access configuration settings
 */
@Injectable()
export class AbsorbWebhookProcessingService {
  /**
   * Logger instance for AbsorbWebhookProcessingService
   */
  private readonly logger = new Logger(AbsorbWebhookProcessingService.name);

  /**
   * Constructor for AbsorbWebhookProcessingService
   *
   * @param webhookService - Service to handle business logic for webhooks
   * @param signatureVerificationService - Service to verify webhook signatures
   */
  constructor(
    private readonly webhookService: AbsorbWebhookService,
    private readonly signatureVerificationService: WebhookSignatureVerificationService,
    private readonly configService: AbsorbConfigService,
  ) {}

  /**
   * Process incoming webhook request with comprehensive security validation
   *
   *
   * @param request - The incoming HTTP request object
   * @param _response - The HTTP response object (not used in processing)
   * @returns A Promise that resolves to an AbsorbWebhookResponse object
   */
  async processWebhook(
    request: RequestWithValidatedHeaders,
    _response: Response,
  ): Promise<AbsorbWebhookResponse> {
    // Generate correlation ID for request tracking
    const correlationId = this.generateCorrelationId();
    // Record start time for processing duration
    const startTime = Date.now();

    // Log request details for debugging
    this.logger.log(
      'Processing webhook request',
      {
        correlationId,
        method: request.method,
        url: request.url,
        userAgent: request.headers['user-agent'],
      },
      'AbsorbWebhookProcessingService',
    );

    try {
      // Initialize processing context
      const context = this.initializeProcessingContext(request, correlationId, startTime);
      this.logger.debug(
        'Webhook processing context initialized',
        {
          correlationId,
        },
        'AbsorbWebhookProcessingService',
      );

      // Validate webhook security
      await this.validateWebhookSecurity(request, context);
      this.logger.debug(
        'Webhook security validation completed successfully',
        {
          correlationId,
        },
        'AbsorbWebhookProcessingService',
      );

      // Process webhook through business logic
      const result = await this.webhookService.processWebhook(context);

      this.logger.log('Webhook processing completed successfully', {
        correlationId,
      });

      return {
        success: result.success,
        message: result.message,
        correlationId,
        processingTime: Date.now() - startTime,
        eventType: result.eventType ?? 'unknown',
      };
    } catch (error) {
      return this.handleProcessingError(error as Error, correlationId, startTime);
    }
  }

  /**
   * Generate a unique correlation ID for request tracking
   */
  private generateCorrelationId(): string {
    return uuidv4();
  }

  /**
   * Initialize webhook processing context with request metadata
   */
  private initializeProcessingContext(
    request: RequestWithValidatedHeaders,
    correlationId: string,
    startTime: number,
  ): AbsorbWebhookProcessingContext {
    // Get validated headers from the guard
    const validatedHeaders = request.validatedHeaders;

    return {
      correlationId,
      startTime,
      headers: {
        signature:
          validatedHeaders?.absorbSignature ?? (request.headers['x-absorb-signature'] as string),
        timestamp:
          validatedHeaders?.xAbsorbTimestamp ?? (request.headers['x-absorb-timestamp'] as string),
        userAgent: validatedHeaders?.userAgent ?? (request.headers['user-agent'] as string),
        contentType: validatedHeaders?.contentType ?? (request.headers['content-type'] as string),
      },
      request: {
        method: request.method,
        url: request.url,
        ip: request.ip ?? 'unknown',
      },
      payload: request.body as Record<string, unknown>, // Add the payload from request body with type assertion
    };
  }

  /**
   * Validate webhook security including signature and timestamp verification
   *
   * @param request - The incoming HTTP request object
   * @param context - The webhook processing context containing headers and metadata
   * @returns A Promise that resolves when validation is complete
   * @throws Error if security validation fails
   */
  private async validateWebhookSecurity(
    request: RequestWithValidatedHeaders,
    context: WebhookProcessingContext,
  ): Promise<void> {
    // console.log('Request', request);
    this.logger.debug('Webhook security validation started', 'AbsorbWebhookProcessingService');
    const { signature, timestamp } = context.headers;

    // Check signature requirement
    if (this.configService.isSignatureVerificationEnabled() && !signature) {
      this.logger.error('Missing required security header: signature', {
        signature,
      });
      throw new Error('Missing required security header: signature');
    }

    // Check timestamp requirement only if timestamp validation is enabled
    if (this.configService.isTimestampValidationEnabled() && !timestamp) {
      this.logger.error('Missing required security header: timestamp', {
        timestamp,
      });
      throw new Error('Missing required security header: timestamp');
    }
    this.logger.debug(
      'Webhook security validation passed',
      {
        signature,
        timestamp,
      },
      'AbsorbWebhookProcessingService',
    );

    // Verify webhook signature
    await this.signatureVerificationService.verifyWebhookSignature(
      request,
      signature,
      timestamp,
      context,
    );

    this.logger.debug('Webhook security validation passed', 'AbsorbWebhookProcessingService');
  }

  /**
   * Handle processing errors and create standardized error response
   *
   * @param error - The error that occurred during webhook processing
   * @param correlationId - The unique identifier for the webhook request
   * @param startTime - The timestamp when the webhook processing started
   * @returns A standardized AbsorbWebhookResponse object with error details
   */
  private handleProcessingError(
    error: Error,
    correlationId: string,
    startTime: number,
  ): AbsorbWebhookResponse {
    const processingTime = Date.now() - startTime;

    this.logger.error(`Webhook processing failed after ${processingTime}ms: ${error.message}`, {
      correlationId,
      error: error instanceof Error ? error.stack : 'Unknown error',
    });

    return {
      success: false,
      message: 'Webhook processing failed',
      correlationId,
      processingTime,
      error: {
        type: error instanceof Error ? error.constructor.name : 'UnknownError',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
    };
  }
}
