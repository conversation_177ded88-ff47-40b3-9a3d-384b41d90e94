import { ActiveStatusEnum } from '../../enums';

/**
 * Group entity schema
 */
export interface GroupEntity {
  readonly id: string; // GUID - group identifier
  readonly name: string; // group name
  readonly isAutomatic: boolean; // whether users are added automatically via rules
  readonly userIds: string[]; // Array of GUID user identifiers (max 50,000)
  readonly dateEdited: string; // ISO 8601 datetime
  readonly dateAdded: string; // ISO 8601 datetime
}

/**
 * Department entity schema
 */
export interface DepartmentEntity {
  readonly id: string; // GUID - department identifier
  readonly name: string; // department name
  readonly externalId?: string; // external system reference
  readonly parentDepartmentId?: string; // GUID - for hierarchical structure
  readonly activeStatus: ActiveStatusEnum;
  readonly dateEdited: string; // ISO 8601 datetime
  readonly dateAdded: string; // ISO 8601 datetime
}

/**
 * Curriculum group entity
 */
export interface CurriculumGroupEntity {
  readonly id: string; // GUID
  readonly name: string; // curriculum group name
  readonly dateEdited: string; // ISO 8601 datetime
  readonly dateAdded: string; // ISO 8601 datetime
}
