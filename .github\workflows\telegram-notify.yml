name: Telegram Notifications

on:
  push:
  pull_request:
  issues:
  issue_comment:
  release:

jobs:
  notify:
    runs-on: ubuntu-latest
    steps:
      - name: Send Telegram notification
        uses: appleboy/telegram-action@master
        with:
          to: ${{ secrets.TELEGRAM_CHAT_ID }}
          token: ${{ secrets.TELEGRAM_BOT_TOKEN }}
          message: |
            🔔 GitHub Activity in ${{ github.repository }}
            
            Event: ${{ github.event_name }}
            Actor: ${{ github.actor }}
            Ref: ${{ github.ref }}
            
            ${{ github.event.head_commit.message || github.event.pull_request.title || github.event.issue.title }}
            
            View: ${{ github.event.head_commit.url || github.event.pull_request.html_url || github.event.issue.html_url }}