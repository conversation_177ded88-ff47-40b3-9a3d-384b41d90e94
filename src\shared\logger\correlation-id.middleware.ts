import { Injectable, NestMiddleware } from '@nestjs/common';
import { NextFunction, Request, Response } from 'express';
import { v4 as uuidv4 } from 'uuid';
import { LoggerService } from './logger.service';

export interface RequestWithCorrelation extends Request {
  correlationId: string;
}

/**
 * Middleware to generate and attach correlation ID to requests
 *
 * @description
 * This middleware generates a unique correlation ID for each incoming request
 * and attaches it to the request object. It also sets the correlation ID in
 * the response header for tracking purposes.
 *
 * @remarks
 * The correlation ID is generated using UUIDv4 and is attached to the request
 * object as `req.correlationId`. It can be used for logging, tracing, and
 * correlating requests across microservices.
 *
 * @see {@link LoggerService} for logging details
 * @see {@link RequestWithCorrelation} for request object extension
 */
@Injectable()
export class CorrelationIdMiddleware implements NestMiddleware {
  constructor(private readonly _logger: LoggerService) {}

  use(req: RequestWithCorrelation, res: Response, next: NextFunction): void {
    // Get correlation ID from header or generate new one
    const correlationId =
      (req.headers['x-correlation-id'] as string) ??
      (req.headers['x-request-id'] as string) ??
      uuidv4();

    // Attach correlation ID to request
    req.correlationId = correlationId;

    // Set correlation ID in response header
    res.setHeader('x-correlation-id', correlationId);

    // Set context for logger
    this._logger.setLogContext({
      correlationId,
      requestId: correlationId,
      method: req.method,
      url: req.url,
      userAgent: req.headers['user-agent'],
      ip: req.ip ?? req.connection.remoteAddress,
    });

    // Log request start
    this._logger.logStructured(
      'log',
      'Request started',
      {
        method: req.method,
        url: req.url,
        correlationId,
      },
      'CorrelationIdMiddleware',
    );

    // Track request timing
    const startTime = Date.now();

    // Override res.end to log request completion
    const originalEnd = res.end.bind(res);
    const logger = this._logger;
    res.end = function (this: Response, ...args: unknown[]): Response {
      const duration = Date.now() - startTime;

      // Log request completion
      logger.logApiRequest(
        {
          method: req.method,
          url: req.url,
          statusCode: res.statusCode,
          duration,
        },
        'CorrelationIdMiddleware',
      );

      // Call original end method with all arguments
      return originalEnd.apply(this, args as Parameters<typeof originalEnd>);
    }.bind(res);

    next();
  }
}
