/**
 * Common type definitions for AbsorbLMS
 */

// User type definition
export type UserType = 'Learner' | 'Instructor' | 'Admin' | 'Manager';

// Course type definition
export type CourseType = 'OnlineCourse' | 'InstructorLedCourse' | 'CourseBundle' | 'Curriculum';

// HTTP status types
export type HttpStatusCode = 200 | 201 | 400 | 401 | 403 | 404 | 422 | 429 | 500;

// Duration object interface
export interface DurationObject {
  readonly years: number;
  readonly months: number;
  readonly days: number;
  readonly hours: number;
}

// Price resource interface
export interface PriceResource {
  readonly id: string; // GUID
  readonly departmentId: string; // GUID - department-specific pricing
  readonly currency: string; // currency code
  readonly amount: number; // decimal price
}

// Course enrollment credits model
export interface CourseEnrollmentCreditsModel {
  readonly creditTypeId: string; // GUID
  readonly credits: number; // decimal
}

// Department selection definition
export interface DepartmentSelectionDefinition {
  readonly departmentId: string; // GUID
  readonly includeSubDepartments: boolean;
}
