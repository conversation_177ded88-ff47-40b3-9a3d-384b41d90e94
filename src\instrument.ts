import * as sentry from '@sentry/nestjs';
import { nodeProfilingIntegration } from '@sentry/profiling-node';
import { config } from 'dotenv';
import { resolve } from 'path';
// Use unknown types for Sentry events to avoid type conflicts

/**
 * Sentry instrumentation file
 * This file must be imported before any other modules in the application
 * to ensure proper error tracking and performance monitoring
 */

// Load environment variables before accessing them
// This ensures Sentry configuration is available before NestJS starts
const nodeEnv = process.env['NODE_ENV'] ?? 'development';
config({ path: resolve(process.cwd(), '.env.local') }); // Local overrides
config({ path: resolve(process.cwd(), `.env.${nodeEnv}`) }); // Environment specific
config({ path: resolve(process.cwd(), '.env') }); // Default fallback

// Get environment variables for Sentry configuration
const SENTRY_DSN = process.env['SENTRY_DSN'];
const SENTRY_ENVIRONMENT = process.env['SENTRY_ENVIRONMENT'] ?? 'development';
const SENTRY_ENABLED = process.env['SENTRY_ENABLED'] === 'true';
const SENTRY_TRACES_SAMPLE_RATE = parseFloat(process.env['SENTRY_TRACES_SAMPLE_RATE'] ?? '0.1');
const SENTRY_PROFILES_SAMPLE_RATE = parseFloat(process.env['SENTRY_PROFILES_SAMPLE_RATE'] ?? '0.1');
const SENTRY_ENABLE_LOGS = process.env['SENTRY_ENABLE_LOGS'] === 'true';

/**
 * Check if the exception is an HTTP client error (4xx status code)
 */
function isHttpClientError(exception: unknown): boolean {
  if (!exception || typeof exception !== 'object') {
    return false;
  }

  const hasStatus = 'status' in exception;
  if (!hasStatus) {
    return false;
  }

  const status = (exception as { status: unknown }).status;
  return typeof status === 'number' && status >= 400 && status < 500;
}

/**
 * Check if the exception is a validation error
 */
function isValidationError(exception: unknown): boolean {
  if (!exception || typeof exception !== 'object') {
    return false;
  }

  const hasName = 'name' in exception;
  if (!hasName) {
    return false;
  }

  const name = (exception as { name: unknown }).name;
  return name === 'ValidationError' || name === 'BadRequestException';
}

// Only initialize Sentry if enabled and DSN is provided
if (SENTRY_ENABLED && SENTRY_DSN) {
  const sentryConfig = {
    dsn: SENTRY_DSN,
    environment: SENTRY_ENVIRONMENT,
    // Setting this option to true will send default PII data to Sentry
    // For example, automatic IP address collection on events
    sendDefaultPii: true,
    integrations: [
      // Add profiling integration for performance monitoring
      nodeProfilingIntegration(),
      // Add console logging integration for structured logs
      sentry.consoleLoggingIntegration({
        levels: ['error'], // Only capture serious errors
      }),
    ],
    // Set tracing sample rate for performance monitoring
    tracesSampleRate: SENTRY_TRACES_SAMPLE_RATE,
    // Set profiling sample rate (relative to tracesSampleRate)
    profilesSampleRate: SENTRY_PROFILES_SAMPLE_RATE,
    // Enable structured logging if configured
    enableLogs: SENTRY_ENABLE_LOGS,
    // Configure error filtering to only send serious errors
    // eslint-disable-next-line @typescript-eslint/explicit-function-return-type
    beforeSend(event: unknown, hint: unknown) {
      // Type guard for hint with originalException
      if (!hint || typeof hint !== 'object' || !('originalException' in hint)) {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/no-unsafe-return
        return event as any;
      }

      const typedHint = hint as { originalException: unknown };
      const exception = typedHint.originalException;

      // Check if it's an HTTP client error (4xx)
      if (isHttpClientError(exception)) {
        return null;
      }

      // Check if it's a validation error
      if (isValidationError(exception)) {
        return null;
      }

      // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/no-unsafe-return
      return event as any;
    },
    // Set server name to help identify the source
    serverName: process.env['HOSTNAME'] ?? 'psychscene-bridge',
  };

  // Add release if available
  if (process.env['npm_package_version']) {
    const configWithRelease = sentryConfig as typeof sentryConfig & { release?: string };
    configWithRelease.release = process.env['npm_package_version'];
  }

  sentry.init(sentryConfig);

  // eslint-disable-next-line no-console
  console.log(`Sentry initialized for environment: ${SENTRY_ENVIRONMENT}`);
} else {
  // eslint-disable-next-line no-console
  console.log('Sentry is disabled or DSN not provided');
}
