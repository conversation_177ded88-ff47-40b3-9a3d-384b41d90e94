import { IsDateString, IsNotEmpty, IsOptional, IsString } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

/**
 * User-related webhook event data
 */
export class AbsorbUserWebhookDataDto {
  @ApiProperty({
    description: 'User ID in Absorb LMS',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  @IsNotEmpty()
  @IsString()
  userId: string;

  @ApiProperty({
    description: 'User email address',
    example: '<EMAIL>',
  })
  @IsNotEmpty()
  @IsString()
  email: string;

  @ApiProperty({
    description: 'User first name',
    example: '<PERSON>',
    required: false,
  })
  @IsOptional()
  @IsString()
  firstName?: string;

  @ApiProperty({
    description: 'User last name',
    example: 'Doe',
    required: false,
  })
  @IsOptional()
  @IsString()
  lastName?: string;

  @ApiProperty({
    description: 'User status',
    example: 'Active',
    required: false,
  })
  @IsOptional()
  @IsString()
  status?: string;
}

/**
 * Enrollment-related webhook event data
 */
export class AbsorbEnrollmentWebhookDataDto {
  @ApiProperty({
    description: 'Enrollment ID in Absorb LMS',
    example: '550e8400-e29b-41d4-a716-446655440001',
  })
  @IsNotEmpty()
  @IsString()
  enrollmentId: string;

  @ApiProperty({
    description: 'User ID associated with enrollment',
    example: '550e8400-e29b-41d4-a716-446655440002',
  })
  @IsNotEmpty()
  @IsString()
  userId: string;

  @ApiProperty({
    description: 'Course ID for the enrollment',
    example: '550e8400-e29b-41d4-a716-446655440003',
  })
  @IsNotEmpty()
  @IsString()
  courseId: string;

  @ApiProperty({
    description: 'Enrollment status',
    example: 'Completed',
    required: false,
  })
  @IsOptional()
  @IsString()
  status?: string;

  @ApiProperty({
    description: 'Completion date',
    example: '2024-12-20T10:30:00Z',
    required: false,
  })
  @IsOptional()
  @IsDateString()
  completedAt?: string;

  @ApiProperty({
    description: 'Score achieved',
    example: 85.5,
    required: false,
  })
  @IsOptional()
  score?: number;
}

/**
 * Course-related webhook event data
 */
export class AbsorbCourseWebhookDataDto {
  @ApiProperty({
    description: 'Course ID in Absorb LMS',
    example: '550e8400-e29b-41d4-a716-446655440004',
  })
  @IsNotEmpty()
  @IsString()
  courseId: string;

  @ApiProperty({
    description: 'Course name',
    example: 'Introduction to Safety Training',
  })
  @IsNotEmpty()
  @IsString()
  name: string;

  @ApiProperty({
    description: 'Course description',
    example: 'Basic safety training for all employees',
    required: false,
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    description: 'Course status',
    example: 'Published',
    required: false,
  })
  @IsOptional()
  @IsString()
  status?: string;
}

/**
 * AbsorbLMS webhook verification DTO
 * Based on official documentation using 'absorb-signature' header
 */
export class AbsorbWebhookVerificationDto {
  @ApiProperty({
    description: 'AbsorbLMS webhook signature from absorb-signature header',
    example: 'sha256=abc123def456...',
  })
  @IsNotEmpty()
  @IsString()
  absorbSignature: string;

  @ApiProperty({
    description: 'Raw request body for HMAC-SHA256 signature verification',
  })
  @IsNotEmpty()
  @IsString()
  rawBody: string;

  @ApiProperty({
    description: 'Webhook secret key for signature verification',
  })
  @IsNotEmpty()
  @IsString()
  webhookSecret: string;

  @ApiProperty({
    description: 'Timestamp of the request for replay attack prevention',
    example: '2023-01-01T00:00:00.000Z',
    required: false,
  })
  @IsOptional()
  @IsDateString()
  timestamp?: string;
}
