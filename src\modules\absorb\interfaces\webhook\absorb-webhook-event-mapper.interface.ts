import { ActionType, EntityType } from '../../enums';
import { AbsorbBaseWebhookPayload } from './absorb-base-webhook-payload.interface';
import { AbsorbRawWebhookPayload } from './absorb-raw-webhook-payload.interface';

/**
 * Interface for mapping raw Absorb events to your standardized format
 */
export interface AbsorbWebhookEventMapper {
  /**
   * Maps raw Absorb webhook payload to your standardized format
   */
  mapToStandardFormat(
    rawPayload: AbsorbRawWebhookPayload,
    correlationId: string,
  ): AbsorbBaseWebhookPayload;

  /**
   * Extracts entity type from raw event data
   */
  extractEntityType(rawPayload: AbsorbRawWebhookPayload): EntityType;

  /**
   * Extracts action type from raw event data
   */
  extractActionType(rawPayload: AbsorbRawWebhookPayload): ActionType;
}
