import { WebhookEventType } from '@/modules/absorb/enums';
import { BaseWebhookPayload } from '../absorb-base-webhook-payload.interface';

/**
 * Certificate event payloads
 */
export interface CertificateAwardedPayload extends BaseWebhookPayload {
  readonly eventType: WebhookEventType.CERTIFICATE_AWARDED;
  readonly certificateId: string; // GUID - certificate identifier
  readonly certificateUrl?: string; // URL to certificate
  readonly expiryDate?: string; // ISO 8601 datetime
}
