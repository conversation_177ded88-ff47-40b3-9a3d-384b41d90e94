import { Global, Module } from '@nestjs/common';
import { ConfigModule } from '../../config';
import { LoggerService } from './logger.service';
import { CorrelationIdMiddleware } from './correlation-id.middleware';
import { LoggerInterceptor } from './logger.interceptor';

/**
 * Logger module
 * This module provides logging services and middleware for the application
 */
@Global()
@Module({
  imports: [ConfigModule],
  providers: [LoggerService, CorrelationIdMiddleware, LoggerInterceptor],
  exports: [LoggerService, CorrelationIdMiddleware, LoggerInterceptor],
})
export class LoggerModule {}
