import { Controller, Get, HttpCode, HttpStatus, Post, Query } from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { AbsorbOAuthService } from '../services/absorb-oauth.service';
import {
  AbsorbOAuthAuthorizationUrlDto,
  AbsorbOAuthCallbackDto,
  AbsorbOAuthCallbackResponseDto,
  AbsorbOAuthErrorResponseDto,
} from '../dto/oauth.dto';

/**
 * Absorb OAuth 2.0 controller
 * This controller handles OAuth 2.0 authorization flow for Absorb LMS integration
 * Implements authorization code flow as per Absorb Integration API v2 documentation
 */
@ApiTags('absorb-oauth')
@Controller('absorb/oauth')
export class AbsorbOAuthController {
  constructor(private readonly _absorbOAuthService: AbsorbOAuthService) {}

  /**
   * Generate OAuth authorization URL
   * Step 1 of OAuth flow - generates URL to redirect user to Absorb login page
   * @param config OAuth configuration parameters
   * @returns Authorization URL and state for security verification
   */
  @Post('authorize')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Generate OAuth authorization URL',
    description: 'Creates authorization URL for OAuth 2.0 flow with Absorb LMS',
  })
  @ApiResponse({
    status: 200,
    description: 'Authorization URL generated successfully',
    type: AbsorbOAuthAuthorizationUrlDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid OAuth configuration',
    type: AbsorbOAuthErrorResponseDto,
  })
  generateAuthorizationUrl(): AbsorbOAuthAuthorizationUrlDto {
    return this._absorbOAuthService.generateAuthorizationUrl();
  }

  /**
   * OAuth callback endpoint
   * Step 2.5 of OAuth flow - handles redirect from Absorb with authorization code
   * @param callbackParams Query parameters from Absorb redirect
   * @returns Callback response with success status and tokens or error details
   */
  @Get('callback')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Handle OAuth callback from Absorb',
    description:
      'Processes the OAuth callback redirect from Absorb and exchanges authorization code for tokens',
  })
  @ApiResponse({
    status: 200,
    description: 'OAuth callback processed successfully',
    type: AbsorbOAuthCallbackResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid callback parameters or authorization failed',
    type: AbsorbOAuthCallbackResponseDto,
  })
  async handleOAuthCallback(
    @Query() callbackParams: AbsorbOAuthCallbackDto,
  ): Promise<AbsorbOAuthCallbackResponseDto> {
    return this._absorbOAuthService.handleOAuthCallback(callbackParams);
  }
}
