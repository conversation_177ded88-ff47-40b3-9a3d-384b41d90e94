import { MiddlewareConsumer, Module, NestModule } from '@nestjs/common';
import { APP_FILTER } from '@nestjs/core';
import { SentryModule } from '@sentry/nestjs/setup';
import { AppController } from './app.controller';
import { SentryExceptionFilter } from './common/filters/sentry-exception.filter';
import { ConfigModule } from './config';
import { CorrelationIdMiddleware, LoggerModule } from './shared/logger';
import { AbsorbModule } from './modules/absorb/absorb.module';

@Module({
  imports: [SentryModule.forRoot(), ConfigModule, LoggerModule, AbsorbModule],
  controllers: [AppController],
  providers: [
    {
      provide: APP_FILTER,
      useClass: SentryExceptionFilter,
    },
  ],
})
export class AppModule implements NestModule {
  configure(consumer: MiddlewareConsumer): void {
    consumer.apply(CorrelationIdMiddleware).forRoutes('*path');
  }
}
