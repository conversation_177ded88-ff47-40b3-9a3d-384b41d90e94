import { IsBoolean, IsOptional, IsString } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

/**
 * Department creation DTO
 */
export class CreateDepartmentDto {
  @ApiProperty({ description: 'Department name', example: 'Engineering' })
  @IsString()
  name: string;

  @ApiProperty({ description: 'External system ID', example: 'ENG001', required: false })
  @IsOptional()
  @IsString()
  externalId?: string;

  @ApiProperty({
    description: 'Parent department ID (GUID)',
    example: '550e8400-e29b-41d4-a716-************',
    required: false,
  })
  @IsOptional()
  @IsString()
  parentDepartmentId?: string;
}

/**
 * Group creation DTO
 */
export class CreateGroupDto {
  @ApiProperty({ description: 'Group name', example: 'Safety Team' })
  @IsString()
  name: string;

  @ApiProperty({ description: 'Is automatic group', example: false })
  @IsBoolean()
  isAutomatic: boolean;

  @ApiProperty({
    description: 'User IDs (GUIDs)',
    example: ['550e8400-e29b-41d4-a716-************'],
    required: false,
  })
  @IsOptional()
  @IsString({ each: true })
  userIds?: string[];
}

/**
 * Competency response DTO
 */
export class CompetencyResponseDto {
  @ApiProperty({
    description: 'Competency ID (GUID)',
    example: '550e8400-e29b-41d4-a716-************',
  })
  id: string;

  @ApiProperty({ description: 'Competency name', example: 'Safety Awareness' })
  name: string;

  @ApiProperty({ description: 'Competency level', example: 3 })
  competencyLevel: number;

  @ApiProperty({ description: 'Category name', example: 'Safety', required: false })
  categoryName?: string;

  @ApiProperty({
    description: 'Date acquired (ISO 8601)',
    example: '2024-01-15T10:30:00Z',
    required: false,
  })
  dateAcquired?: string;
}

/**
 * Certificate response DTO
 */
export class CertificateResponseDto {
  @ApiProperty({
    description: 'Certificate ID (GUID)',
    example: '550e8400-e29b-41d4-a716-************',
  })
  id: string;

  @ApiProperty({ description: 'Certificate name', example: 'Safety Training Certificate' })
  name: string;

  @ApiProperty({
    description: 'Certificate URL',
    example: 'https://example.com/cert/123',
    required: false,
  })
  url?: string;

  @ApiProperty({ description: 'Date awarded (ISO 8601)', example: '2024-01-15T10:30:00Z' })
  dateAwarded: string;

  @ApiProperty({
    description: 'Expiry date (ISO 8601)',
    example: '2025-01-15T10:30:00Z',
    required: false,
  })
  expiryDate?: string;

  @ApiProperty({ description: 'User ID (GUID) who earned certificate' })
  userId: string;

  @ApiProperty({ description: 'Course ID (GUID) that awarded certificate' })
  courseId: string;
}
