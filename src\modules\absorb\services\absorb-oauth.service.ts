import { BadRequestException, Injectable, UnauthorizedException } from '@nestjs/common';
import { ConfigService } from '@/config';
import { LoggerService } from '@/shared/logger';
import {
  AbsorbOAuthAuthorizationUrlDto,
  AbsorbOAuthCallbackDto,
  AbsorbOAuthCallbackResponseDto,
  AbsorbOAuthConfigDto,
  AbsorbOAuthTokenRequestDto,
  AbsorbOAuthTokenResponseDto,
} from '../dto/oauth.dto';
import { AbsorbOAuthUtils } from '../utils/oauth.utils';

interface StoredOAuthToken {
  access_token: string;
  refresh_token: string;
  expires_at: Date;
  token_type: string;
}

/**
 * Absorb OAuth 2.0 service
 * Implements OAuth 2.0 authorization code flow for Absorb LMS integration
 * Based on Absorb Integration API v2 OAuth documentation
 */
@Injectable()
export class AbsorbOAuthService {
  private storedToken: StoredOAuthToken | null = null;

  constructor(
    private readonly configService: ConfigService,
    private readonly logger: LoggerService,
  ) {
    this.logger.setContext(AbsorbOAuthService.name);
  }

  /**
   * Generate OAuth authorization URL for user authentication
   * Step 1 of OAuth flow - redirects user to Absorb login page
   * @param config OAuth configuration parameters
   * @returns Authorization URL and state for security verification
   */
  generateAuthorizationUrl(): AbsorbOAuthAuthorizationUrlDto {
    this.logger.log('Generating OAuth authorization URL');
    //get config from env
    const config: AbsorbOAuthConfigDto = {
      client_id: AbsorbOAuthUtils.getClientIdFromConfig(this.configService),
      client_secret: AbsorbOAuthUtils.getClientSecretFromConfig(this.configService),
      redirect_uri: AbsorbOAuthUtils.getRedirectUriFromConfig(this.configService),
      base_url: AbsorbOAuthUtils.getBaseUrlFromConfig(this.configService),
    };

    // Generate secure random state for CSRF protection
    const state = AbsorbOAuthUtils.generateSecureState();

    // Manually construct query parameters to avoid double-encoding of redirect_uri
    const queryParams = [
      `client_id=${encodeURIComponent(config.client_id)}`,
      `client_secret=${encodeURIComponent(config.client_secret)}`,
      // Use the redirect_uri directly without encoding it again
      `redirect_uri=${config.redirect_uri}`,
      'response_type=code',
      'scope=admin.v1',
      `state=${encodeURIComponent(state)}`,
    ].join('&');
    //example
    /* Example */
    //https://clientRoute.com/oauth/authorize?client_id=_ahKweh2tdKdNMKnecwo&client_secret=iBHVbc0fn7vdCgvMxDAy7fWNlpQCwAcQoeCNYEDboahcUime01&redirect_uri=https://example.com&response_type=code&scope=admin.v1&state=anyString
    //base_url/oauth/authorize?client_id=value&client_secret=value&client_secret=value&response_type=code&scope=admin.v1&state=anyString
    const authorizationUrl = `${config.base_url}/oauth/authorize?${queryParams}`;

    this.logger.debug(`Generated authorization URL for client: ${config.client_id}`);

    return {
      authorization_url: authorizationUrl,
      state: state,
    };
  }

  /**
   * Handle OAuth callback from Absorb
   * Processes the callback parameters and exchanges authorization code for tokens
   * @param callbackParams OAuth callback query parameters
   * @returns Callback response with success status and tokens or error details
   */
  async handleOAuthCallback(
    callbackParams: AbsorbOAuthCallbackDto,
  ): Promise<AbsorbOAuthCallbackResponseDto> {
    this.logger.log('Processing OAuth callback from Absorb');

    // Validate callback parameters and check for errors
    const validationError = AbsorbOAuthUtils.validateCallbackParameters(callbackParams);
    if (validationError) {
      return validationError;
    }

    try {
      const tokenRequest = AbsorbOAuthUtils.createTokenRequest(
        callbackParams.code!,
        this.configService,
      );
      const tokens = await this.exchangeCodeForToken(tokenRequest);

      this.logger.log('OAuth callback processed successfully');
      return {
        success: true,
        message: 'Authorization successful. Tokens have been exchanged.',
        tokens,
      };
    } catch (error) {
      this.logger.error('Failed to exchange authorization code for tokens', error);
      return {
        success: false,
        message: 'Failed to exchange authorization code for tokens',
        error: {
          code: 'token_exchange_failed',
          description: error instanceof Error ? error.message : 'Unknown error occurred',
        },
      };
    }
  }

  /**
   * Exchange authorization code for refresh token
   * Step 2 of OAuth flow - gets refresh token using authorization code
   * @param tokenRequest Token exchange request parameters
   * @returns OAuth token response with access and refresh tokens
   */
  async exchangeCodeForToken(
    tokenRequest: AbsorbOAuthTokenRequestDto,
  ): Promise<AbsorbOAuthTokenResponseDto> {
    this.logger.log('Exchanging authorization code for tokens');

    if (tokenRequest.grant_type !== 'authorization_code') {
      throw new BadRequestException('Invalid grant type for code exchange');
    }

    if (!tokenRequest.code) {
      throw new BadRequestException('Authorization code is required');
    }

    const baseUrl = AbsorbOAuthUtils.getBaseUrlFromConfig(this.configService);
    const tokenResponse = await this.performTokenRequest(baseUrl, tokenRequest);

    // Store the token for future use
    this.storeToken(tokenResponse);

    this.logger.log('Successfully exchanged authorization code for tokens');
    return tokenResponse;
  }

  /**
   * Get new access token using refresh token
   * Step 3 of OAuth flow - refreshes access token when expired
   * @param tokenRequest Token refresh request parameters
   * @returns New OAuth token response
   */
  async refreshAccessToken(
    tokenRequest: AbsorbOAuthTokenRequestDto,
  ): Promise<AbsorbOAuthTokenResponseDto> {
    this.logger.log('Refreshing access token');

    if (tokenRequest.grant_type !== 'refresh_token') {
      throw new BadRequestException('Invalid grant type for token refresh');
    }

    if (!tokenRequest.refresh_token) {
      throw new BadRequestException('Refresh token is required');
    }

    const baseUrl = AbsorbOAuthUtils.getBaseUrlFromConfig(this.configService);
    const tokenResponse = await this.performTokenRequest(baseUrl, tokenRequest);

    // Update stored token
    this.storeToken(tokenResponse);

    this.logger.log('Successfully refreshed access token');
    return tokenResponse;
  }

  /**
   * Get current valid access token
   * Automatically refreshes if token is expired or expiring soon
   * @returns Valid access token
   */
  async getValidAccessToken(): Promise<string> {
    if (!this.storedToken) {
      throw new UnauthorizedException('No OAuth token available. Please authenticate first.');
    }

    // Check if token is expired or expiring soon (within 5 minutes)
    if (this.isTokenExpiringSoon(this.storedToken)) {
      this.logger.log('Access token expiring soon, refreshing...');

      const refreshRequest: AbsorbOAuthTokenRequestDto = {
        grant_type: 'refresh_token',
        client_id: AbsorbOAuthUtils.getClientIdFromConfig(this.configService),
        client_secret: AbsorbOAuthUtils.getClientSecretFromConfig(this.configService),
        refresh_token: this.storedToken.refresh_token,
        nonce: AbsorbOAuthUtils.generateNonce(),
      };

      await this.refreshAccessToken(refreshRequest);
    }

    return this.storedToken.access_token;
  }

  /**
   * Get authentication status
   * @returns Whether user is authenticated and token expiration info
   */
  getAuthenticationStatus(): {
    isAuthenticated: boolean;
    expiresAt?: string;
    timeUntilExpiry?: number;
  } {
    if (!this.storedToken) {
      return { isAuthenticated: false };
    }

    const now = new Date();
    const isValid = this.storedToken.expires_at > now;
    const timeUntilExpiry = Math.max(
      0,
      Math.floor((this.storedToken.expires_at.getTime() - now.getTime()) / 1000),
    );

    return {
      isAuthenticated: isValid,
      expiresAt: this.storedToken.expires_at.toISOString(),
      timeUntilExpiry: isValid ? timeUntilExpiry : 0,
    };
  }

  /**
   * Clear stored OAuth tokens (logout)
   */
  clearTokens(): void {
    this.logger.log('Clearing stored OAuth tokens');
    this.storedToken = null;
  }

  /**
   * Perform OAuth token request to Absorb API
   * @param baseUrl Absorb API base URL
   * @param tokenRequest Token request parameters
   * @returns OAuth token response
   */
  private async performTokenRequest(
    baseUrl: string,
    tokenRequest: AbsorbOAuthTokenRequestDto,
  ): Promise<AbsorbOAuthTokenResponseDto> {
    const url = `${baseUrl}/oauth/token`;

    // Create form data for the token request with special handling for redirect_uri
    const formData = new FormData();

    // Add all parameters to the form data
    Object.entries(tokenRequest).forEach(([key, value]: [string, string | undefined]) => {
      if (value !== undefined && value !== null) {
        formData.append(key, value);
      }
    });

    // Convert FormData to URLSearchParams for the fetch request
    const params = new URLSearchParams();
    for (const [key, value] of Array.from(formData.entries())) {
      params.append(key, value as string);
    }

    const headers = {
      'Content-Type': 'application/x-www-form-urlencoded',
      'x-api-version': 'v1',
    };

    this.logger.log(`Making token request to: ${url}`);
    this.logger.log(`Token request headers:`, JSON.stringify(headers));
    this.logger.log(`Token request body:`, JSON.stringify(Object.fromEntries(params.entries())));

    try {
      const response = await fetch(url, {
        method: 'POST',
        headers,
        body: params,
      });

      if (!response.ok) {
        const errorText = await response.text();
        this.logger.error(`OAuth token request failed: ${response.status} - ${errorText}`);
        throw new UnauthorizedException(`OAuth authentication failed: ${response.statusText}`);
      }

      const tokenData = await response.json();
      this.logger.debug('OAuth token request successful');

      return tokenData as AbsorbOAuthTokenResponseDto;
    } catch (error) {
      this.logger.error('OAuth token request error:', error);
      if (error instanceof UnauthorizedException) {
        throw error;
      }
      throw new UnauthorizedException('Failed to authenticate with Absorb OAuth service');
    }
  }

  /**
   * Store OAuth token with calculated expiration
   * @param tokenResponse OAuth token response from API
   */
  private storeToken(tokenResponse: AbsorbOAuthTokenResponseDto): void {
    const expiresAt = new Date();
    expiresAt.setSeconds(expiresAt.getSeconds() + tokenResponse.expires_in);

    this.storedToken = {
      access_token: tokenResponse.access_token,
      refresh_token: tokenResponse.refresh_token,
      expires_at: expiresAt,
      token_type: tokenResponse.token_type,
    };

    this.logger.debug(`Token stored, expires at: ${expiresAt.toISOString()}`);
  }

  /**
   * Check if token is expired or expiring soon (within 5 minutes)
   * @param token Stored OAuth token
   * @returns True if token needs refresh
   */
  private isTokenExpiringSoon(token: StoredOAuthToken): boolean {
    const now = new Date();
    const fiveMinutesFromNow = new Date(now.getTime() + 5 * 60 * 1000);
    return token.expires_at <= fiveMinutesFromNow;
  }
}
