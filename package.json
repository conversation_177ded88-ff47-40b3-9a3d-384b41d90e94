{"name": "psychscene-bridge", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "build:prod": "nest build --webpack", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "format:check": "prettier --check \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "lint:check": "eslint \"{src,apps,libs,test}/**/*.ts\"", "lint:report": "eslint \"{src,apps,libs,test}/**/*.ts\" --format json --output-file eslint-report.json", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:cov:ci": "jest --coverage --ci --watchAll=false --passWithNoTests", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "test:e2e:cov": "jest --config ./test/jest-e2e.json --coverage", "typecheck": "tsc --noEmit", "typecheck:watch": "tsc --noEmit --watch", "quality:check": "npm run typecheck && npm run lint:check && npm run format:check && npm run test:cov:ci", "quality:fix": "npm run lint && npm run format", "precommit": "lint-staged", "prepare": "husky", "clean": "rimraf dist coverage eslint-report.json", "clean:install": "npm run clean && rm -rf node_modules package-lock.json && npm install", "docs:generate": "compodoc -p tsconfig.json -s", "docs:build": "compodoc -p tsconfig.json -d docs"}, "dependencies": {"@nestjs/common": "^11.0.1", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.0.1", "@nestjs/platform-express": "^11.0.1", "@nestjs/swagger": "^11.2.0", "@sentry/nestjs": "^10.8.0", "@sentry/profiling-node": "^10.8.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "cross-env": "^10.0.0", "joi": "^18.0.1", "nestjs-pino": "^4.4.0", "pino": "^9.9.0", "pino-http": "^10.5.0", "pino-sentry-transport": "^1.5.1", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1", "uuid": "^11.1.0"}, "devDependencies": {"@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.18.0", "@nestjs/cli": "^11.0.0", "@nestjs/schematics": "^11.0.0", "@nestjs/testing": "^11.0.1", "@types/express": "^5.0.0", "@types/jest": "^30.0.0", "@types/node": "^22.10.7", "@types/supertest": "^6.0.2", "@types/uuid": "^10.0.0", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.2", "globals": "^16.0.0", "husky": "^9.1.7", "jest": "^30.0.0", "lint-staged": "^16.1.5", "pino-pretty": "^13.1.1", "prettier": "^3.4.2", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.2.5", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.7.3", "typescript-eslint": "^8.20.0"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s", "!**/*.spec.ts", "!**/*.e2e-spec.ts", "!**/node_modules/**", "!**/dist/**", "!**/*.interface.ts", "!**/*.dto.ts", "!**/*.entity.ts", "!**/main.ts"], "coverageDirectory": "../coverage", "testEnvironment": "node", "coverageReporters": ["text", "lcov", "html", "json-summary"], "coverageThreshold": {"global": {"branches": 80, "functions": 80, "lines": 80, "statements": 80}}, "testTimeout": 10000, "verbose": true, "detectOpenHandles": true, "forceExit": true, "moduleNameMapping": {"^@/(.*)$": "<rootDir>/$1", "^@/common/(.*)$": "<rootDir>/common/$1", "^@/modules/(.*)$": "<rootDir>/modules/$1", "^@/shared/(.*)$": "<rootDir>/shared/$1", "^@/config/(.*)$": "<rootDir>/config/$1", "^@/types/(.*)$": "<rootDir>/types/$1"}}, "lint-staged": {"*.{ts,js}": ["eslint --fix", "prettier --write"], "*.{json,md,yml,yaml}": ["prettier --write"], "*.ts": ["npm run test -- --findRelatedTests --passWithNoTests"]}}