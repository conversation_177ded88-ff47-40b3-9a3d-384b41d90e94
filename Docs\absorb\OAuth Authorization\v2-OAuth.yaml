x-generator: NSwag v13.0.6.0 (NJsonSchema v10.0.23.0 (Newtonsoft.Json v11.0.0.0))
swagger: '2.0'
info:
  title: Absorb Integration API
  description: "# Overview\r\n\r\nMany of the resources in a client portal are protected and require that the \r\nuser is authenticated. The Absorb Integration API uses openId connect (authorization code flow)\r\n to provide an access token and refresh token.\r\n\r\nThis token must then be sent in the `Authorization` header when making requests \r\nto protected resources.\r\n\r\n```\r\nAuthorization: Bearer <token>\r\n```\r\n\r\nAccess token is valid for 4 hours after it is issued. After the time has elapsed, \r\nanother login request is required to get a new token.\r\n\r\nRefresh token lasts for 1 year.\r\n\r\nAs the token represents an authenticated user, it must be protected. Bearer \r\nauthentication should only be used over HTTPS; Absorb LMS does not support HTTP.\r\n\r\n# OAuth Authorization Code Flow\r\n\r\n## 1. Request Auth Code\r\n\r\nTo request an authorization code, user makes an authentication request to authorization endpoint. Please note that the authorization code is 1-time use\r\n\r\nHeader is not required for requesting an authorization code. This call is not made via postman but entered into a browser.\r\n\r\n| Name          | Type   | Description                                                                                                                                    |\r\n|---------------|--------|------------------------------------------------------------------------------------------------------------------------------------------------|\r\n| client_id     | string | The unique API client identifier. Location: Portal Settings -> Integration API OAuth Client ID                                |\r\n| client_secret | string | The unique API client secret identifier. Location: Portal Settings -> Integration API OAuth Client Secret                     |\r\n| redirect_uri  | string | The URI the users are sent back to after authorization. Should always be https.                                                                |\r\n| response_type | string | The value of this field should always be \"code\".                                                                                               |\r\n| scope         | string | Space-delimited list of member permissions your application is requesting. Supported scope: \"admin.v1\"   |\r\n| state         | string | A unique string value of your choice that is hard to guess.                                                                                    |\r\n\r\nIt will redirect to the login page for the user to login. If the login is successful, the consent page will pop up.\r\nAfter the user provides the consent, the server will redirect to the URL provided in the `redirect_uri` query parameter and provide the authorization code.\r\n\r\n```\r\n/* Example */\r\n\r\nhttps://clientRoute.com/oauth/authorize?client_id=_ahKweh2tdKdNMKnecwo&client_secret=iBHVbc0fn7vdCgvMxDAy7fWNlpQCwAcQoeCNYEDboahcUime01&redirect_uri=https://example.com&response_type=code&scope=admin.v1&state=anyString\r\n \r\n```\r\n\r\n## 2. Get Refresh Token\r\n\r\nExchange an authorization code for a refresh token. Make a request to token endpoint, providing the authorization code received in the previous step.\r\n\r\n`POST rootUrl/oauth/token`\r\n\r\n### Headers\r\n| Name\t\t\t| Description\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t  |\r\n|---------------|-------------------------------------------------------------------------------------------------------------------------------------|\r\n| x-api-version     | The value of this header should always be \"v1\".\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t  |\r\n\r\n### Body\r\n| Name          | Type   | Description                                                                                                                |\r\n|---------------|--------|----------------------------------------------------------------------------------------------------------------------------|\r\n| grant_type    | string | The value of this field should always be \"authorization_code\".                                                             |\r\n| client_id     | string | Portal Settings -> Integration API OAuth Client ID                                                                           |\r\n| client_secret | string | Portal Settings -> Integration API OAuth Client Secret                                                                       |\r\n| code          | string | The authorization code received in previous step.                                                                          |\r\n| nonce         | string | String value used to associate a client session with an ID Token, and to mitigate replay attacks                           |\r\n| redirect_uri  | string | The URI the users are sent back to after authorization. Should always be https.                                            |\r\n\r\nThe following is an example of a successful token response.\r\n\r\n```\r\n\r\n{\r\n    \"access_token\": \"**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\",\r\n    \"token_type\": \"bearer\",\r\n    \"expires_in\": 14399,\r\n    \"refresh_token\": \"09RR0D5mp5J56i4GTPEjdhMyMhw6t5IhTad5ErDD\"\r\n}\r\n```\r\n\r\n| Name          | Type   | Description                                                                                                                |\r\n|---------------|--------|----------------------------------------------------------------------------------------------------------------------------|\r\n| access_token  | string | The access token for the application.                                                                                      |\r\n| token_type    | string | The type of this access token.                                                                                             |\r\n| expires_in    | string | The number of seconds remaining until the token expires.                                                                   |\r\n| refresh_token | string | The refresh token, can be used to acquire new access tokens.                                                               |\r\n\r\n\r\n## 3. Get Access Token\r\n\r\nRecreate an access token using a refresh token to login on behalf of a given user. Access tokens only last 4 hours. Once access token is expired, you can use the refresh token to acquire a new access token.\r\nYou can also use refresh token to acquire an access token on behalf of another learner.\r\n\r\n`POST rootUrl/oauth/token`\r\n\r\n### Headers\r\n| Name\t\t\t| Description\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t  |\r\n|---------------|-------------------------------------------------------------------------------------------------------------------------------------|\r\n| x-api-version     | The value of this header should always be \"v1\".\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t                                      |\r\n\r\n### Body\r\n| Name          | Type   | Description                                                                                                                |\r\n|---------------|--------|----------------------------------------------------------------------------------------------------------------------------|\r\n| grant_type    | string | The value of this field should always be \"refresh_token\".                                                                  |\r\n| client_id     | string | Portal Settings -> Integration API OAuth Client ID                                                                                            |\r\n| client_secret | string | Portal Settings -> Integration API OAuth Client Secret                                                                     |\r\n| refresh_token | string | The refresh token issued by the server in previous step.                                                                   |\r\n| nonce         | string | String value used to associate a client session with an ID Token, and to mitigate replay attacks                           |\r\n| on-behalf-of  | string | (Optional) The username of another user you would like to be logged in as                                                  |\r\n| scope         | string | Space-delimited list of member permissions your application is requesting. Supported scope: \"admin.v1\"                     |\r\n\r\n## 4. Make Request Using Access Token\r\n\r\nOnce you've obtained an access token, you can start making authenticated API requests\r\n by including an Authorization header in the HTTP call.\r\n\r\n```\r\n/* Example */\r\n\r\nGET my-profile\r\nAuthorization Bearer {access_token}\r\n```\r\n\r\nIf you are using the access token on behalf of another user, you will see the info of the user you are on behalf of \r\nin the response."
  version: ''
consumes:
  - x-www-form-urlencoded
produces:
  - application/json
paths:
  /oauth/authorize:
    get:
      tags:
        - OAuth
      summary: Request Authorization Code
      description: >-
        Make an authentication request to authorization endpoint to get an
        authorization code.
      operationId: OAuth_Authorize
      parameters:
        - type: string
          name: client_id
          in: query
          description: >-
            The unique API client identifier. Location: Portal Settings ->
            Integration API OAuth Client ID
          required: true
          format: string
          x-nullable: true
        - type: string
          name: client_secret
          in: query
          description: >-
            The unique API client secret. Location: Portal Settings ->
            Integration API OAuth Client Secret
          required: true
          format: string
          x-nullable: true
        - type: string
          name: redirect_uri
          in: query
          description: >-
            The URI the users are sent back to after authorization. Should
            always be https.
          required: true
          x-nullable: true
        - type: string
          name: response_type
          in: query
          description: The value of this field should always be "code".
          required: true
          x-nullable: true
        - type: string
          name: scope
          in: query
          description: >-
            Space-delimited list of member permissions your application is
            requesting.
          required: true
          x-nullable: true
        - type: string
          name: state
          in: query
          description: A unique string value of your choice that is hard to guess.
          required: true
          x-nullable: true
      responses:
        '200':
          x-nullable: true
          description: ''
  /oauth/token:
    post:
      tags:
        - OAuth
      summary: Get Refresh Token / Access Token
      description: >-
        Make request to get an refresh token or get a token on behalf of user.
        Please refer to "OAuth Authorization Code Flow" part 2 and 3 for more
        details about different types of requests.
      operationId: OAuth_Token
      parameters:
        - type: string
          name: x-api-version
          in: header
          required: true
        - name: request
          in: body
          description: >-
            The request to get an access/refresh token or get an access token on
            behalf of a user.
          schema:
            $ref: '#/definitions/OauthTokenRefreshTokenRequest'
          x-nullable: true
      responses:
        '201':
          x-nullable: false
          description: ''
          schema:
            $ref: '#/definitions/AuthenticationResponseResource'
        '400':
          x-nullable: false
          description: Invalid parameter(s) passed, see error code for further information.
          schema:
            $ref: '#/definitions/AuthenticationErrorResponseResource'
      security:
        - x-api-key: []
      x-amazon-apigateway-integration:
        type: HTTP_PROXY
        connectionType: VPC_LINK
        connectionId: ${stageVariables.vpcLinkId}
        requestParameters:
          integration.request.header.X-Absorb-V1-API-Key: method.request.header.x-api-key
        httpMethod: post
        uri: https://${stageVariables.endpointBaseUrl}:443/oauth/token
definitions:
  OauthTokenRefreshTokenRequest:
    type: object
    description: Request to retrieve Refresh Token
    properties:
      grant_type:
        type: string
        description: >-
          The value of this field should always be "authorization_code" or
          "refresh_token".
      client_id:
        type: string
        description: Portal Settings -&gt; Integration API OAuth Client ID
      client_secret:
        type: string
        description: Portal Settings -&gt; Integration API OAuth Client Secret
      nonce:
        type: string
        description: >-
          String value used to associate a client session with an ID Token, and
          to mitigate replay attacks. *Code property is only used in the get
          refresh token call.*
      code:
        type: string
        description: >-
          The authorization code received in oauth/authorize step. *Code
          property is only used in the get refresh token call.*
      on-behalf-of:
        type: string
        description: >-
          Username you want to sign in as. *On behalf of property is only used
          in the on-behalf-of call call.*
      refresh_token:
        type: string
        description: >-
          Refresh token from get refresh token oauth/token call *Refresh token
          property is only used in the on-behalf-of call call.*
      scope:
        type: string
        description: >-
          Space-delimited list of member permissions your application is
          requesting. *Scope property is only used in the on-behalf-of call
          call.*
  AuthenticationResponseResource:
    type: object
    description: Resource that represents user authentication.
    properties:
      access_token:
        type: string
        description: >-
          Access token that represents an authenticated user. It is required by
          protected API endpoints.


          The token must be included in the `Authorization` header in the
          format:

          ```

          Authorization: Bearer [token]

          ```
      token_type:
        type: string
        description: This value will always be "bearer".
      expires_in:
        type: int
        description: The number of seconds remaining until the token expires.
      refresh_token:
        type: string
        description: The refresh token, can be used to acquire new access tokens.
  AuthenticationErrorResponseResource:
    type: object
    description: Resource that represents an error in user authentication.
    properties:
      error:
        type: string
        description: The specific error code indicating the issue.
securityDefinitions:
  x-api-key:
    type: apiKey
    name: x-api-key
    in: header
x-servers:
  - url: https://rest.myabsorb.com
    description: US
  - url: https://rest.myabsorb.ca
    description: CA
  - url: https://rest.myabsorb.eu
    description: EU
  - url: https://rest.myabsorb.com.au
    description: AU
