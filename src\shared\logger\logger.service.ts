import { ConsoleLogger, Injectable, LogLevel } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import pino from 'pino';
import { AsyncLocalStorage } from 'async_hooks';

export interface LogContext {
  correlationId?: string;
  userId?: string;
  requestId?: string;
  [key: string]: unknown;
}

interface SentryConfig {
  enabled?: boolean;
  dsn?: string;
  environment?: string;
  enableLogs?: boolean;
}

@Injectable()
export class LoggerService extends ConsoleLogger {
  private readonly pinoLogger: pino.Logger;
  private readonly asyncLocalStorage = new AsyncLocalStorage<LogContext>();

  constructor(private readonly _configService: ConfigService) {
    super();
    this.pinoLogger = this._createPinoLogger();
  }

  private _createPinoLogger(): pino.Logger {
    const LOG_LEVEL = this._configService.get<string>('LOG_LEVEL') ?? 'info';
    const NODE_ENV = this._configService.get<string>('NODE_ENV') ?? 'development';

    // Get Sentry configuration
    const sentryConfig = this._configService.get<SentryConfig>('sentry');
    const sentryEnabled = sentryConfig?.enabled && sentryConfig?.dsn;

    const transports = this._createTransports(NODE_ENV, Boolean(sentryEnabled), sentryConfig);
    const pinoOptions = this._createPinoOptions(LOG_LEVEL, transports.length > 0);

    if (transports.length > 0) {
      pinoOptions.transport = {
        targets: transports,
      };
    }

    return pino(pinoOptions);
  }

  private _createPinoOptions(logLevel: string, hasTransports: boolean): pino.LoggerOptions {
    const options: pino.LoggerOptions = {
      level: logLevel,
      redact: ['password', 'token', 'secret', 'authorization', 'apiKey'],
      serializers: {
        req: pino.stdSerializers.req,
        res: pino.stdSerializers.res,
        err: pino.stdSerializers.err,
      },
    };

    // Only add formatters when not using transports
    // Pino doesn't allow custom level formatters with transport targets
    if (!hasTransports) {
      options.formatters = {
        level: (label: string): { level: string } => ({ level: label }),
        log: (object: Record<string, unknown>): Record<string, unknown> => {
          const context = this.asyncLocalStorage.getStore();
          return {
            ...object,
            ...context,
          };
        },
      };
    }

    return options;
  }

  private _createTransports(
    nodeEnv: string,
    sentryEnabled: boolean,
    sentryConfig?: SentryConfig,
  ): pino.TransportSingleOptions[] {
    const transports: pino.TransportSingleOptions[] = [];

    // Add console transport
    transports.push(this._createConsoleTransport(nodeEnv));

    // Add Sentry transport for serious errors only
    if (sentryEnabled && sentryConfig?.enableLogs) {
      transports.push(this._createSentryTransport(sentryConfig, nodeEnv));
    }

    return transports;
  }

  private _createConsoleTransport(nodeEnv: string): pino.TransportSingleOptions {
    if (nodeEnv === 'development') {
      return {
        target: 'pino-pretty',
        options: {
          colorize: true,
          translateTime: 'HH:MM:ss.l',
          ignore: 'pid,hostname',
          messageFormat: '[{context}] {msg}',
          singleLine: true,
        },
      };
    }

    return {
      target: 'pino/file',
      options: {
        destination: 1, // stdout
      },
    };
  }

  private _createSentryTransport(
    sentryConfig: SentryConfig,
    nodeEnv: string,
  ): pino.TransportSingleOptions {
    return {
      target: 'pino-sentry-transport',
      options: {
        sentry: {
          dsn: sentryConfig?.dsn ?? '',
          environment: sentryConfig?.environment ?? nodeEnv,
          beforeSend: this._sentryBeforeSend,
        },
        filter: this._sentryFilter,
      },
    };
  }

  private readonly _sentryBeforeSend = (
    event: Record<string, unknown>,
  ): Record<string, unknown> | null => {
    // Skip if it's a validation error or client error
    const exception = event['exception'] as { values?: Array<{ type?: string }> };
    const tags = event['tags'] as { httpStatus?: string };
    const exceptionType = exception?.values?.[0]?.type;
    const httpStatus = tags?.httpStatus;

    if (
      exceptionType === 'ValidationError' ||
      exceptionType === 'BadRequestException' ||
      httpStatus?.startsWith('4')
    ) {
      return null;
    }
    return event;
  };

  private readonly _sentryFilter = (log: Record<string, unknown>): boolean => {
    // Only send error level and above to Sentry
    const logLevel = log['level'] as number;
    if (logLevel < 50) {
      // 50 is error level in Pino
      return false;
    }
    // Don't send client errors (4xx) to Sentry
    const statusCode = log['statusCode'] as number;
    if (statusCode && statusCode >= 400 && statusCode < 500) {
      return false;
    }
    // Don't send ValidationError or BadRequestException to Sentry
    const error = log['err'] as { name?: string };
    if (error && (error.name === 'ValidationError' || error.name === 'BadRequestException')) {
      return false;
    }
    return true;
  };

  /**
   * Set logging context for correlation
   */
  override setContext(context: string): void {
    super.setContext(context);
  }

  /**
   * Set structured logging context
   */
  setLogContext(context: LogContext): void {
    const currentContext = this.asyncLocalStorage.getStore() ?? {};
    this.asyncLocalStorage.enterWith({ ...currentContext, ...context });
  }

  /**
   * Run function with specific context
   */
  runWithContext<T>(context: LogContext, fn: () => T): T {
    return this.asyncLocalStorage.run(context, fn);
  }

  /**
   * Get current context
   */
  getContext(): LogContext | undefined {
    return this.asyncLocalStorage.getStore();
  }

  /**
   * Override log method to use Pino
   */
  override log(
    message: string,
    contextOrData?: string | Record<string, unknown>,
    context?: string,
  ): void {
    if (typeof contextOrData === 'string') {
      // Traditional usage: log(message, context)
      const logContext = this._buildLogContext(contextOrData);
      this.pinoLogger.info(logContext, message);
    } else {
      // New usage: log(message, data, context) or log(message, data)
      const logContext = this._buildLogContext(context, contextOrData);
      this.pinoLogger.info(logContext, message);
    }
  }

  /**
   * Override error method to use Pino
   */
  override error(
    message: string,
    traceOrData?: string | Record<string, unknown>,
    context?: string,
  ): void {
    if (typeof traceOrData === 'string') {
      // Traditional usage: error(message, trace, context)
      const logContext = this._buildLogContext(context, { stack: traceOrData });
      this.pinoLogger.error(logContext, message);
    } else {
      // New usage: error(message, data, context) or error(message, data)
      const logContext = this._buildLogContext(context, traceOrData);
      this.pinoLogger.error(logContext, message);
    }
  }

  /**
   * Override warn method to use Pino
   */
  override warn(
    message: string,
    contextOrData?: string | Record<string, unknown>,
    context?: string,
  ): void {
    if (typeof contextOrData === 'string') {
      // Traditional usage: warn(message, context)
      const logContext = this._buildLogContext(contextOrData);
      this.pinoLogger.warn(logContext, message);
    } else {
      // New usage: warn(message, data, context) or warn(message, data)
      const logContext = this._buildLogContext(context, contextOrData);
      this.pinoLogger.warn(logContext, message);
    }
  }

  /**
   * Override debug method to use Pino
   */
  override debug(
    message: string,
    contextOrData?: string | Record<string, unknown>,
    context?: string,
  ): void {
    if (typeof contextOrData === 'string') {
      // Traditional usage: debug(message, context)
      const logContext = this._buildLogContext(contextOrData);
      this.pinoLogger.debug(logContext, message);
    } else {
      // New usage: debug(message, data, context) or debug(message, data)
      const logContext = this._buildLogContext(context, contextOrData);
      this.pinoLogger.debug(logContext, message);
    }
  }

  /**
   * Override verbose method to use Pino
   */
  override verbose(
    message: string,
    contextOrData?: string | Record<string, unknown>,
    context?: string,
  ): void {
    if (typeof contextOrData === 'string') {
      // Traditional usage: verbose(message, context)
      const logContext = this._buildLogContext(contextOrData);
      this.pinoLogger.trace(logContext, message);
    } else {
      // New usage: verbose(message, data, context) or verbose(message, data)
      const logContext = this._buildLogContext(context, contextOrData);
      this.pinoLogger.trace(logContext, message);
    }
  }

  /**
   * Custom method for structured logging
   */
  logStructured(
    level: LogLevel,
    message: string,
    data?: Record<string, unknown>,
    context?: string,
  ): void {
    const logContext = this._buildLogContext(context, data);

    switch (level) {
      case 'error':
        this.pinoLogger.error(logContext, message);
        break;
      case 'warn':
        this.pinoLogger.warn(logContext, message);
        break;
      case 'log':
        this.pinoLogger.info(logContext, message);
        break;
      case 'debug':
        this.pinoLogger.debug(logContext, message);
        break;
      case 'verbose':
        this.pinoLogger.trace(logContext, message);
        break;
      default:
        this.pinoLogger.info(logContext, message);
    }
  }

  /**
   * Log API request
   */
  logApiRequest(
    requestData: {
      method: string;
      url: string;
      statusCode: number;
      duration: number;
    },
    context?: string,
  ): void {
    const logContext = this._buildLogContext(context, requestData);

    this.pinoLogger.info(
      logContext,
      `${requestData.method} ${requestData.url} ${requestData.statusCode} - ${requestData.duration}ms`,
    );
  }

  /**
   * Log business event
   */
  logBusinessEvent(event: string, data?: Record<string, unknown>, context?: string): void {
    const logContext = this._buildLogContext(context, {
      event,
      data,
      type: 'business_event',
    });

    this.pinoLogger.info(logContext, `Business event: ${event}`);
  }

  /**
   * Log security event
   */
  logSecurityEvent(event: string, data?: Record<string, unknown>, context?: string): void {
    const logContext = this._buildLogContext(context, {
      event,
      data,
      type: 'security_event',
    });

    this.pinoLogger.warn(logContext, `Security event: ${event}`);
  }

  /**
   * Build log context with current async context
   */
  private _buildLogContext(
    context?: string,
    additionalData?: Record<string, unknown>,
  ): Record<string, unknown> {
    const asyncContext = this.asyncLocalStorage.getStore() ?? {};

    // Extract context from async storage, but prioritize the string parameter
    const { context: asyncContextValue, ...restAsyncContext } = asyncContext;

    return {
      ...restAsyncContext,
      ...additionalData,
      // Ensure context is a string for proper display - prioritize parameter over async context
      context: context ?? (typeof asyncContextValue === 'string' ? asyncContextValue : 'Unknown'),
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * Get Pino logger instance for direct access
   */
  getPinoLogger(): pino.Logger {
    return this.pinoLogger;
  }
}
