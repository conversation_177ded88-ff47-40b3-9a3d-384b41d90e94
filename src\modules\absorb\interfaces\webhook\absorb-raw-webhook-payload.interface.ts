/**
 * Raw Absorb LMS webhook payload (as received from Absorb)
 * This matches exactly what Absorb sends
 */
export interface AbsorbRawWebhookPayload {
  /**
   * ISO 8601 timestamp from Absorb
   * @example "2025-06-12T08:59:21.2464249Z"
   */
  readonly timestamp: string;

  /**
   * Event name as sent by Absorb (may contain spaces)
   * @example "CertificateAwarded", "User Enrolled in Course by Admin"
   */
  readonly eventName: string;

  /**
   * Raw details object from Absorb (structure varies by event)
   */
  readonly details: Record<string, unknown>;
}
