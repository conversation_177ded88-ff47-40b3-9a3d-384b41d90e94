import { Injectable, Logger } from '@nestjs/common';
import { createHmac, timingSafeEqual } from 'crypto';
import { AbsorbConfigService } from '../../config/absorb-config.service';
import { WebhookSecurityContext } from '../../dto/webhook-headers.dto';

/**
 * Webhook Signature Verification Result
 */
export interface SignatureVerificationResult {
  isValid: boolean;
  error?: string | undefined;
  timestamp?: number | undefined;
  computedSignature?: string | undefined;
}

/**
 * Timestamp Validation Result
 */
export interface TimestampValidationResult {
  isValid: boolean;
  error?: string | undefined;
  age?: number | undefined;
}

/**
 * AbsorbLMS Webhook Signature Verification Service
 * Implements secure HMAC-SHA256 signature verification according to AbsorbLMS documentation
 *
 * Security Features:
 * - Timing-safe signature comparison to prevent timing attacks
 * - Replay attack prevention through timestamp validation
 * - Proper error handling without information leakage
 * - Configurable validation settings
 */
@Injectable()
export class WebhookSignatureVerificationService {
  private readonly logger = new Logger(WebhookSignatureVerificationService.name);

  // Security constants
  private readonly SIGNATURE_ALGORITHM = 'sha256';
  private readonly SIGNATURE_ENCODING = 'hex';
  private readonly DEFAULT_TIMESTAMP_TOLERANCE = 300; // 5 minutes in seconds
  private readonly MIN_SIGNATURE_LENGTH = 64; // SHA256 hex length
  private readonly MAX_SIGNATURE_LENGTH = 64;

  constructor(private readonly configService: AbsorbConfigService) {}

  /**
   * Verifies the HMAC-SHA256 signature of a webhook request
   *
   * @param payload - Raw request body as string or Buffer
   * @param signature - HMAC signature from absorb-signature header
   * @param secret - Webhook secret for signature verification
   * @param timestamp - Optional timestamp for replay attack prevention
   * @returns Verification result with detailed information
   */
  async verifySignature(
    payload: string | Buffer,
    signature: string,
    secret?: string,
    timestamp?: number,
  ): Promise<SignatureVerificationResult> {
    try {
      // Input validation
      const validationError = this._validateInputs(payload, signature, secret);
      if (validationError) {
        return { isValid: false, error: validationError };
      }

      // Get webhook secret
      const webhookSecret = this._getWebhookSecret(secret);
      if (!webhookSecret) {
        return { isValid: false, error: 'Webhook secret not configured' };
      }

      // Verify signature
      const signatureResult = this._performSignatureVerification(payload, signature, webhookSecret);
      if (!signatureResult.isValid) {
        return signatureResult;
      }

      // Validate timestamp if needed
      const timestampResult = this._validateTimestampIfRequired(timestamp);
      if (!timestampResult.isValid) {
        return timestampResult;
      }

      this.logger.debug('Webhook signature verification successful');
      return {
        isValid: true,
        error: undefined,
        timestamp,
        computedSignature: signatureResult.computedSignature,
      };
    } catch (error) {
      this.logger.error('Error during signature verification', {
        error: error.message,
        stack: error.stack,
      });
      return {
        isValid: false,
        error: 'Signature verification failed due to internal error',
        timestamp: undefined,
        computedSignature: undefined,
      };
    }
  }

  /**
   * Gets webhook secret from parameter or config
   */
  private _getWebhookSecret(secret?: string): string | null {
    const webhookSecret = secret ?? this.configService.getWebhookSecret();
    if (!webhookSecret) {
      this.logger.error('Webhook secret not configured');
      return null;
    }
    return webhookSecret;
  }

  /**
   * Performs signature verification logic
   */
  private _performSignatureVerification(
    payload: string | Buffer,
    signature: string,
    webhookSecret: string,
  ): SignatureVerificationResult {
    // Normalize signature (remove any prefix and convert to lowercase)
    const normalizedSignature = this._normalizeSignature(signature);
    if (!normalizedSignature) {
      return { isValid: false, error: 'Invalid signature format' };
    }

    // Compute expected signature
    const computedSignature = this._computeSignature(payload, webhookSecret);

    // Timing-safe comparison to prevent timing attacks
    const isSignatureValid = this._timingSafeCompare(normalizedSignature, computedSignature);

    if (!isSignatureValid) {
      this.logger.warn('Webhook signature verification failed', {
        expectedLength: computedSignature.length,
        receivedLength: normalizedSignature.length,
        payloadSize: Buffer.isBuffer(payload) ? payload.length : payload.length,
      });
      return {
        isValid: false,
        error: 'Invalid signature',
        computedSignature,
      };
    }

    return { isValid: true, computedSignature };
  }

  /**
   * Validates timestamp if required
   */
  private _validateTimestampIfRequired(timestamp?: number): SignatureVerificationResult {
    if (timestamp && this.configService.isTimestampValidationEnabled()) {
      const timestampResult = this._validateTimestamp(timestamp);
      if (!timestampResult.isValid) {
        return {
          isValid: false,
          error: timestampResult.error,
          timestamp,
        };
      }
    }
    return { isValid: true };
  }

  /**
   * Verify webhook signature with security validation
   * Includes HMAC-SHA256 verification and timestamp validation
   */
  async verifyWebhookSignature(
    request: any,
    signature: string,
    timestamp: string,
    context: any,
  ): Promise<boolean> {
    try {
      const correlationId = context.correlationId ?? 'unknown';
      this.logger.debug('Verifying webhook signature', {
        correlationId,
        hasSignature: !!signature,
        hasTimestamp: !!timestamp,
      });

      if (!signature || !timestamp) {
        this.logger.warn('Missing required headers', {
          correlationId,
          hasSignature: !!signature,
          hasTimestamp: !!timestamp,
        });
        return false;
      }

      const timestampNum = parseInt(timestamp, 10);
      if (isNaN(timestampNum)) {
        this.logger.warn('Invalid timestamp format', {
          correlationId,
          timestamp,
        });
        return false;
      }

      const rawBody = request.rawBody ?? request.body ?? '';
      const result = await this.verifySignature(rawBody, signature, undefined, timestampNum);

      this.logger.debug('Signature verification completed', {
        correlationId,
        isValid: result.isValid,
        error: result.error,
      });

      return result.isValid;
    } catch (error) {
      this.logger.error('Webhook signature verification failed', {
        error: error.message,
        stack: error.stack,
      });
      return false;
    }
  }

  /**
   * Creates a security context from webhook headers
   *
   * @param headers - Validated webhook headers
   * @returns Security context for further processing
   */
  createSecurityContext(headers: unknown): WebhookSecurityContext {
    const headersObj = headers as Record<string, string>;
    const context: WebhookSecurityContext = {
      signature: headersObj['absorb-signature'] ?? '',
    };

    this._extractTimestamp(headersObj, context);
    this._extractClientIp(headersObj, context);
    this._extractUserAgent(headersObj, context);
    this._extractTraceId(headersObj, context);

    return context;
  }

  /**
   * Extracts timestamp from headers
   *
   * @param headersObj - Webhook headers object
   * @param context - Webhook security context
   */
  private _extractTimestamp(
    headersObj: Record<string, string>,
    context: WebhookSecurityContext,
  ): void {
    if (headersObj['x-absorb-timestamp']) {
      context.timestamp = parseInt(headersObj['x-absorb-timestamp'], 10);
    }
  }

  /**
   * Extracts client IP from headers
   *
   * @param headersObj - Webhook headers object
   * @param context - Webhook security context
   */
  private _extractClientIp(
    headersObj: Record<string, string>,
    context: WebhookSecurityContext,
  ): void {
    const forwardedFor = headersObj['x-forwarded-for'];
    if (forwardedFor && typeof forwardedFor === 'string') {
      const clientIp = forwardedFor.split(',')[0]?.trim();
      if (clientIp) {
        context.clientIp = clientIp;
      }
    }
  }

  /**
   * Extracts user agent from headers
   *
   * @param headersObj - Webhook headers object
   * @param context - Webhook security context
   */
  private _extractUserAgent(
    headersObj: Record<string, string>,
    context: WebhookSecurityContext,
  ): void {
    if (headersObj['user-agent']) {
      context.userAgent = headersObj['user-agent'];
    }
  }

  /**
   * Extracts trace ID from headers
   *
   * @param headersObj - Webhook headers object
   * @param context - Webhook security context
   */
  private _extractTraceId(
    headersObj: Record<string, string>,
    context: WebhookSecurityContext,
  ): void {
    const traceparent = headersObj['traceparent'];
    if (traceparent) {
      const traceMatch = traceparent.match(/^00-([a-f0-9]{32})-/);
      if (traceMatch?.[1]) {
        context.traceId = traceMatch[1];
      }
    }
  }

  /**
   * Validates timestamp for replay attack prevention
   *
   * @param timestamp - Unix timestamp to validate
   * @returns Validation result
   * @throws Error if timestamp validation fails
   */
  private _validateTimestamp(timestamp: number): TimestampValidationResult {
    const now = Math.floor(Date.now() / 1000);
    const age = now - timestamp;
    const tolerance =
      this.configService.getTimestampTolerance() ?? this.DEFAULT_TIMESTAMP_TOLERANCE;

    if (age > tolerance) {
      return {
        isValid: false,
        error: `Request too old: ${age}s > ${tolerance}s`,
        age,
      };
    }

    if (age < -tolerance) {
      return {
        isValid: false,
        error: `Request from future: ${Math.abs(age)}s > ${tolerance}s`,
        age,
      };
    }

    return { isValid: true, age };
  }

  /**
   * Validates input parameters
   *
   * @param payload - Webhook payload
   * @param signature - Webhook signature
   * @param _secret - Optional secret key (not used in validation)
   * @returns Error message if invalid, null if valid
   */
  private _validateInputs(
    payload: string | Buffer,
    signature: string,
    _secret?: string,
  ): string | null {
    if (!payload) {
      return 'Payload is required';
    }

    if (!signature || typeof signature !== 'string') {
      return 'Signature is required and must be a string';
    }

    if (
      signature.length < this.MIN_SIGNATURE_LENGTH ||
      signature.length > this.MAX_SIGNATURE_LENGTH + 10
    ) {
      // Allow for prefixes
      return 'Invalid signature length';
    }

    return null;
  }

  /**
   * Normalizes signature by removing prefixes and converting to lowercase
   *
   * @param signature - Signature to normalize
   * @returns Normalized signature or null if invalid
   */
  private _normalizeSignature(signature: string): string | null {
    try {
      // Remove common prefixes like 'sha256=' if present
      const cleaned = signature.replace(/^(sha256=|hmac-sha256=)/i, '').toLowerCase();

      // Validate hex format
      if (!/^[a-f0-9]{64}$/.test(cleaned)) {
        return null;
      }

      return cleaned;
    } catch {
      return null;
    }
  }

  /**
   * Computes HMAC-SHA256 signature
   *
   * @param payload - Payload to sign
   * @param secret - Secret key for HMAC
   * @returns Computed signature
   */
  private _computeSignature(payload: string | Buffer, secret: string): string {
    const hmac = createHmac(this.SIGNATURE_ALGORITHM, secret);
    hmac.update(payload);
    return hmac.digest(this.SIGNATURE_ENCODING);
  }

  /**
   * Performs timing-safe comparison of two signatures
   *
   * @param signature1 - First signature to compare
   * @param signature2 - Second signature to compare
   * @returns True if signatures match, false otherwise
   * @throws Error if signature comparison fails
   *
   */
  private _timingSafeCompare(signature1: string, signature2: string): boolean {
    try {
      // Ensure both signatures are the same length
      if (signature1.length !== signature2.length) {
        return false;
      }

      // Convert to buffers for timing-safe comparison
      const buf1 = Buffer.from(signature1, 'hex');
      const buf2 = Buffer.from(signature2, 'hex');

      return timingSafeEqual(buf1, buf2);
    } catch {
      return false;
    }
  }
}
