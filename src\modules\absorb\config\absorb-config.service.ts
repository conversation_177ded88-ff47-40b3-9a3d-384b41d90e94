import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { IAbsorbLmsConfig, IAbsorbWebhookConfig } from './absorb-config.interface';

/**
 * Absorb Configuration Service
 * Provides centralized access to Absorb LMS configuration settings
 * Includes webhook security configuration and validation
 */
@Injectable()
export class AbsorbConfigService {
  private readonly logger = new Logger(AbsorbConfigService.name);
  private readonly config: IAbsorbLmsConfig;

  constructor(private readonly configService: ConfigService) {
    this.config = this.loadConfiguration();
    this.validateConfiguration();
  }

  /**
   * Get webhook secret for signature verification
   * @returns Webhook secret string
   */
  getWebhookSecret(): string | undefined {
    return this.config.webhook?.secret ?? this.configService.get<string>('ABSORB_WEBHOOK_SECRET');
  }

  /**
   * Check if signature verification is enabled
   * @returns True if signature verification is enabled
   */
  isSignatureVerificationEnabled(): boolean {
    return this.config.webhook?.enableSignatureVerification ?? true;
  }

  /**
   * Check if timestamp validation is enabled
   * @returns True if timestamp validation is enabled
   */
  isTimestampValidationEnabled(): boolean {
    return this.config.webhook?.enableTimestampValidation ?? false;
  }

  /**
   * Get timestamp tolerance in seconds
   * @returns Timestamp tolerance in seconds (default: 300 = 5 minutes)
   */
  getTimestampTolerance(): number {
    const toleranceMs = this.config.webhook?.timestampToleranceMs ?? 300000;
    return Math.floor(toleranceMs / 1000);
  }

  /**
   * Get complete webhook configuration
   * @returns Webhook configuration object
   */
  getWebhookConfig(): IAbsorbWebhookConfig | undefined {
    return this.config.webhook;
  }

  /**
   * Get API base URL
   * @returns API base URL
   */
  getApiBaseUrl(): string {
    return this.config.apiBaseUrl;
  }

  /**
   * Get API key
   * @returns API key
   */
  getApiKey(): string {
    return this.config.apiKey;
  }

  /**
   * Get API version
   * @returns API version
   */
  getApiVersion(): string {
    return this.config.apiVersion;
  }

  /**
   * Get authentication credentials
   * @returns Authentication credentials
   */
  getAuthCredentials(): IAbsorbLmsConfig['auth'] {
    return this.config.auth;
  }

  /**
   * Load configuration from environment variables and config service
   * @returns Complete Absorb LMS configuration
   */
  private loadConfiguration(): IAbsorbLmsConfig {
    const baseConfig: IAbsorbLmsConfig = {
      apiBaseUrl: this.configService.get<string>('ABSORB_API_BASE_URL', ''),
      apiKey: this.configService.get<string>('ABSORB_API_KEY', ''),
      apiVersion: this.configService.get<string>('ABSORB_API_VERSION', 'v2'),
      auth: {
        username: this.configService.get<string>('ABSORB_USERNAME', ''),
        password: this.configService.get<string>('ABSORB_PASSWORD', ''),
        privateKey: this.configService.get<string>('ABSORB_PRIVATE_KEY', ''),
      },
    };

    // Load webhook configuration if webhook secret is provided
    const webhookSecret = this.configService.get<string>('ABSORB_WEBHOOK_SECRET');
    if (webhookSecret) {
      baseConfig.webhook = {
        secret: webhookSecret,
        enableSignatureVerification: this.configService.get<boolean>(
          'ABSORB_WEBHOOK_SIGNATURE_VERIFICATION',
          true,
        ),
        enableTimestampValidation: this.configService.get<boolean>(
          'ABSORB_WEBHOOK_TIMESTAMP_VALIDATION',
          false,
        ),
        timestampToleranceMs: this.configService.get<number>(
          'ABSORB_WEBHOOK_TIMESTAMP_TOLERANCE_MS',
          300000, // 5 minutes
        ),
      };
    }

    // Load OAuth configuration if available
    const oauthClientId = this.configService.get<string>('ABSORB_OAUTH_CLIENT_ID');
    if (oauthClientId) {
      baseConfig.oauth = {
        baseUrl: this.configService.get<string>('ABSORB_OAUTH_BASE_URL', ''),
        clientId: oauthClientId,
        clientSecret: this.configService.get<string>('ABSORB_OAUTH_CLIENT_SECRET', ''),
        redirectUri: this.configService.get<string>('ABSORB_OAUTH_REDIRECT_URI', ''),
      };
    }

    // Load Integration API configuration
    baseConfig.integrationApi = {
      baseUrl: this.configService.get<string>(
        'ABSORB_INTEGRATION_API_BASE_URL',
        baseConfig.apiBaseUrl,
      ),
      timeout: this.configService.get<number>('ABSORB_INTEGRATION_API_TIMEOUT', 30000),
      retryAttempts: this.configService.get<number>('ABSORB_INTEGRATION_API_RETRY_ATTEMPTS', 3),
      retryDelay: this.configService.get<number>('ABSORB_INTEGRATION_API_RETRY_DELAY', 1000),
    };

    return baseConfig;
  }

  /**
   * Validate required configuration fields
   * @throws Error if required configuration is missing
   */
  private validateConfiguration(): void {
    const errors: string[] = [];

    this.validateApiConfiguration(errors);
    this.validateAuthConfiguration(errors);
    this.validateWebhookConfiguration(errors);
    this.validateOAuthConfiguration(errors);

    if (errors.length > 0) {
      const errorMessage = `Absorb configuration validation failed: ${errors.join(', ')}`;
      this.logger.error(errorMessage);
      throw new Error(errorMessage);
    }

    this.logger.log('Absorb configuration validated successfully');
  }

  /**
   * Validate basic API configuration
   */
  private validateApiConfiguration(errors: string[]): void {
    if (!this.config.apiBaseUrl) {
      errors.push('ABSORB_API_BASE_URL is required');
    }

    if (!this.config.apiKey) {
      errors.push('ABSORB_API_KEY is required');
    }
  }

  /**
   * Validate authentication configuration
   */
  private validateAuthConfiguration(errors: string[]): void {
    if (!this.config.auth.username) {
      errors.push('ABSORB_USERNAME is required');
    }

    if (!this.config.auth.password) {
      errors.push('ABSORB_PASSWORD is required');
    }

    if (!this.config.auth.privateKey) {
      errors.push('ABSORB_PRIVATE_KEY is required');
    }
  }

  /**
   * Validate webhook configuration
   */
  private validateWebhookConfiguration(errors: string[]): void {
    if (this.config.webhook?.enableSignatureVerification && !this.config.webhook.secret) {
      errors.push('ABSORB_WEBHOOK_SECRET is required when signature verification is enabled');
    }
  }

  /**
   * Validate OAuth configuration
   */
  private validateOAuthConfiguration(errors: string[]): void {
    if (this.config.oauth) {
      if (!this.config.oauth.baseUrl) {
        errors.push('ABSORB_OAUTH_BASE_URL is required when OAuth is configured');
      }
      if (!this.config.oauth.clientSecret) {
        errors.push('ABSORB_OAUTH_CLIENT_SECRET is required when OAuth is configured');
      }
      if (!this.config.oauth.redirectUri) {
        errors.push('ABSORB_OAUTH_REDIRECT_URI is required when OAuth is configured');
      }
    }
  }

  /**
   * Check if webhook configuration is properly set up
   * @returns True if webhook is properly configured
   */
  isWebhookConfigured(): boolean {
    return !!this.config.webhook?.secret;
  }

  /**
   * Check if OAuth is configured
   * @returns True if OAuth is configured
   */
  isOAuthConfigured(): boolean {
    return !!this.config.oauth?.clientId;
  }

  /**
   * Get environment-specific configuration summary for logging
   * @returns Configuration summary (without sensitive data)
   */
  getConfigSummary(): Record<string, unknown> {
    return {
      apiVersion: this.config.apiVersion,
      hasApiKey: !!this.config.apiKey,
      hasWebhookSecret: !!this.config.webhook?.secret,
      signatureVerificationEnabled: this.config.webhook?.enableSignatureVerification ?? false,
      timestampValidationEnabled: this.config.webhook?.enableTimestampValidation ?? false,
      timestampToleranceMs: this.config.webhook?.timestampToleranceMs ?? 300000,
      hasOAuth: !!this.config.oauth,
      integrationApiTimeout: this.config.integrationApi?.timeout ?? 30000,
    };
  }
}
