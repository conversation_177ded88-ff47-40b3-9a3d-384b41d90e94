import { WebhookEventType } from '@/modules/absorb/enums';
import { AbsorbBaseWebhookPayload } from '../absorb-base-webhook-payload.interface';

/**
 * Course deactivated payload
 */
export interface CourseDeactivatedPayload extends AbsorbBaseWebhookPayload {
  readonly eventType: WebhookEventType.ONLINE_COURSE_DEACTIVATED | WebhookEventType.ILC_DEACTIVATED;
  readonly courseName: string;
  readonly deactivationDate: string; // ISO 8601 datetime
}
