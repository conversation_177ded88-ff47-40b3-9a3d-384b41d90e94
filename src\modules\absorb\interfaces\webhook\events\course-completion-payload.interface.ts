import { WebhookEventType } from '@/modules/absorb/enums';
import { BaseWebhookPayload } from '../absorb-base-webhook-payload.interface';

/**
 * Course completion payload with enhanced data
 */
export interface CourseCompletionPayload extends BaseWebhookPayload {
  readonly eventType: WebhookEventType.COURSE_COMPLETION;
  readonly completionDate: string; // ISO 8601 datetime
  readonly finalScore?: number; // decimal percentage
  readonly timeSpent?: string; // duration format
  readonly creditsEarned?: number; // decimal credits
}
