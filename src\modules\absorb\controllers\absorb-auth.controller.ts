import { Controller, HttpCode, HttpStatus, Post } from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { AbsorbAuthService } from '../services/absorb-auth.service';
import { AbsorbAuthResponseDto } from '../dto/auth.dto';

/**
 * Absorb authentication controller
 * This controller handles authentication requests for Absorb LMS integration
 */
@ApiTags('absorb-auth')
@Controller('absorb/auth')
export class AbsorbAuthController {
  constructor(private readonly _absorbAuthService: AbsorbAuthService) {}

  /**
   * Authenticate with Absorb LMS and get access token
   * @param credentials Optional authentication credentials (uses config if not provided)
   * @returns Authentication token and metadata
   */
  @Post('login')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Authenticate with Absorb LMS',
    description: 'Authenticate using provided credentials or environment configuration',
  })
  @ApiResponse({
    status: 200,
    description: 'Authentication successful',
    type: AbsorbAuthResponseDto,
  })
  @ApiResponse({ status: 401, description: 'Authentication failed' })
  @ApiResponse({ status: 400, description: 'Invalid credentials format' })
  async authenticate(): Promise<AbsorbAuthResponseDto> {
    return this._absorbAuthService.authenticate();
  }
}
