import { Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { firstValueFrom } from 'rxjs';
import { IAbsorbIntegrationApiConfig } from '../config/absorb-config.interface';

/**
 * Absorb Integration API Service
 * Handles fetching additional data from Absorb Integration API
 * Used to enrich lean webhook payloads with detailed information
 */
@Injectable()
export class AbsorbIntegrationApiService {
  private readonly logger = new Logger(AbsorbIntegrationApiService.name);
  private readonly config: IAbsorbIntegrationApiConfig;
  private readonly apiKey: string;
  private readonly baseUrl: string;

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
  ) {
    this.config = this.configService.get<IAbsorbIntegrationApiConfig>('absorb.integrationApi') ?? {
      baseUrl: this.configService.get<string>('absorb.apiBaseUrl', ''),
      timeout: 30000,
      retryAttempts: 3,
      retryDelay: 1000,
    };
    this.apiKey = this.configService.get<string>('absorb.apiKey', '');
    this.baseUrl = this.config.baseUrl;
  }

  /**
   * Fetch user details by user ID
   * Used when processing user-related webhook events
   */
  async fetchUserDetails(userId: string): Promise<any> {
    this.logger.debug(`Fetching user details for userId: ${userId}`);

    try {
      const response = await this.makeApiCall(`/users/${userId}`);
      this.logger.debug(`Successfully fetched user details for userId: ${userId}`);
      return response.data;
    } catch (error) {
      this.logger.error(`Failed to fetch user details for userId: ${userId}`, error);
      throw error;
    }
  }

  /**
   * Fetch course details by course ID
   * Used when processing course-related webhook events
   */
  async fetchCourseDetails(courseId: string): Promise<any> {
    this.logger.debug(`Fetching course details for courseId: ${courseId}`);

    try {
      const response = await this.makeApiCall(`/courses/${courseId}`);
      this.logger.debug(`Successfully fetched course details for courseId: ${courseId}`);
      return response.data;
    } catch (error) {
      this.logger.error(`Failed to fetch course details for courseId: ${courseId}`, error);
      throw error;
    }
  }

  /**
   * Fetch enrollment details by enrollment ID
   * Used when processing enrollment-related webhook events
   */
  async fetchEnrollmentDetails(enrollmentId: string): Promise<any> {
    this.logger.debug(`Fetching enrollment details for enrollmentId: ${enrollmentId}`);

    try {
      const response = await this.makeApiCall(`/enrollments/${enrollmentId}`);
      this.logger.debug(
        `Successfully fetched enrollment details for enrollmentId: ${enrollmentId}`,
      );
      return response.data;
    } catch (error) {
      this.logger.error(
        `Failed to fetch enrollment details for enrollmentId: ${enrollmentId}`,
        error,
      );
      throw error;
    }
  }

  /**
   * Fetch department details by department ID
   * Used when processing department-related webhook events
   */
  async fetchDepartmentDetails(departmentId: string): Promise<any> {
    this.logger.debug(`Fetching department details for departmentId: ${departmentId}`);

    try {
      const response = await this.makeApiCall(`/departments/${departmentId}`);
      this.logger.debug(
        `Successfully fetched department details for departmentId: ${departmentId}`,
      );
      return response.data;
    } catch (error) {
      this.logger.error(
        `Failed to fetch department details for departmentId: ${departmentId}`,
        error,
      );
      throw error;
    }
  }

  /**
   * Fetch group details by group ID
   * Used when processing group-related webhook events
   */
  async fetchGroupDetails(groupId: string): Promise<any> {
    this.logger.debug(`Fetching group details for groupId: ${groupId}`);

    try {
      const response = await this.makeApiCall(`/groups/${groupId}`);
      this.logger.debug(`Successfully fetched group details for groupId: ${groupId}`);
      return response.data;
    } catch (error) {
      this.logger.error(`Failed to fetch group details for groupId: ${groupId}`, error);
      throw error;
    }
  }

  /**
   * Fetch curriculum details by curriculum ID
   * Used when processing curriculum-related webhook events
   */
  async fetchCurriculumDetails(curriculumId: string): Promise<any> {
    this.logger.debug(`Fetching curriculum details for curriculumId: ${curriculumId}`);

    try {
      const response = await this.makeApiCall(`/curriculums/${curriculumId}`);
      this.logger.debug(
        `Successfully fetched curriculum details for curriculumId: ${curriculumId}`,
      );
      return response.data;
    } catch (error) {
      this.logger.error(
        `Failed to fetch curriculum details for curriculumId: ${curriculumId}`,
        error,
      );
      throw error;
    }
  }

  /**
   * Fetch certificate details by certificate ID
   * Used when processing certificate-related webhook events
   */
  async fetchCertificateDetails(certificateId: string): Promise<any> {
    this.logger.debug(`Fetching certificate details for certificateId: ${certificateId}`);

    try {
      const response = await this.makeApiCall(`/certificates/${certificateId}`);
      this.logger.debug(
        `Successfully fetched certificate details for certificateId: ${certificateId}`,
      );
      return response.data;
    } catch (error) {
      this.logger.error(
        `Failed to fetch certificate details for certificateId: ${certificateId}`,
        error,
      );
      throw error;
    }
  }

  /**
   * Fetch competency details by competency ID
   * Used when processing competency-related webhook events
   */
  async fetchCompetencyDetails(competencyId: string): Promise<any> {
    this.logger.debug(`Fetching competency details for competencyId: ${competencyId}`);

    try {
      const response = await this.makeApiCall(`/competencies/${competencyId}`);
      this.logger.debug(
        `Successfully fetched competency details for competencyId: ${competencyId}`,
      );
      return response.data;
    } catch (error) {
      this.logger.error(
        `Failed to fetch competency details for competencyId: ${competencyId}`,
        error,
      );
      throw error;
    }
  }

  /**
   * Fetch learning path details by learning path ID
   * Used when processing learning path-related webhook events
   */
  async fetchLearningPathDetails(learningPathId: string): Promise<any> {
    this.logger.debug(`Fetching learning path details for learningPathId: ${learningPathId}`);

    try {
      const response = await this.makeApiCall(`/learning-paths/${learningPathId}`);
      this.logger.debug(
        `Successfully fetched learning path details for learningPathId: ${learningPathId}`,
      );
      return response.data;
    } catch (error) {
      this.logger.error(
        `Failed to fetch learning path details for learningPathId: ${learningPathId}`,
        error,
      );
      throw error;
    }
  }

  /**
   * Generic method to make API calls to Absorb Integration API
   * Includes retry logic and proper error handling
   */
  private async makeApiCall(endpoint: string, retryCount = 0): Promise<any> {
    const url = `${this.baseUrl}${endpoint}`;
    const headers = {
      Authorization: `Bearer ${this.apiKey}`,
      'Content-Type': 'application/json',
      Accept: 'application/json',
    };

    try {
      const response = await firstValueFrom(
        this.httpService.get(url, {
          headers,
          timeout: this.config.timeout ?? 30000,
        }),
      );

      return response;
    } catch (error) {
      const maxRetries = this.config.retryAttempts ?? 3;

      if (retryCount < maxRetries) {
        this.logger.warn(
          `API call failed, retrying (${retryCount + 1}/${maxRetries}): ${endpoint}`,
          error.message,
        );

        // Wait before retrying
        await this.delay(this.config.retryDelay ?? 1000);
        return this.makeApiCall(endpoint, retryCount + 1);
      }

      this.logger.error(`API call failed after ${maxRetries} retries: ${endpoint}`, {
        error: error.message,
        status: error.response?.status,
        statusText: error.response?.statusText,
      });

      throw error;
    }
  }

  /**
   * Utility method to add delay for retry logic
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Health check method to verify Integration API connectivity
   */
  async healthCheck(): Promise<boolean> {
    try {
      this.logger.debug('Performing Integration API health check');
      await this.makeApiCall('/health');
      this.logger.debug('Integration API health check passed');
      return true;
    } catch (error) {
      this.logger.error('Integration API health check failed', error);
      return false;
    }
  }
}
