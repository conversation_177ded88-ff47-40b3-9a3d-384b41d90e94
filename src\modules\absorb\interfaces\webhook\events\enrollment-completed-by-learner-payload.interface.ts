import { WebhookEventType } from '@/modules/absorb/enums';
import { BaseWebhookPayload } from '../absorb-base-webhook-payload.interface';

/**
 * Enrollment completed by learner payload
 */
export interface EnrollmentCompletedByLearnerPayload extends BaseWebhookPayload {
  readonly eventType: WebhookEventType.ENROLLMENT_COMPLETED_BY_LEARNER;
  readonly completionDate: string; // ISO 8601 datetime
  readonly finalScore?: number;
  readonly timeSpent?: string;
}
