# Absorb LMS Webhook Setup Guide

This guide explains how to configure webhooks in Absorb LMS to integrate with your NestJS application.

## Overview

Webhooks allow Absorb LMS to send real-time notifications to your application when specific events occur, such as:
- User registration and updates
- Course enrollments and completions
- Certificate issuance
- Course creation and modifications

## Prerequisites

1. Admin access to your Absorb LMS instance
2. Your NestJS application deployed and accessible via HTTPS
3. Webhook endpoint URL: `https://your-domain.com/api/v1/absorb/webhooks`

## Absorb LMS Webhook Configuration

### Step 1: Access Webhook Settings

1. Log in to your Absorb LMS admin panel
2. Navigate to **Account** > **Integrations** > **Webhooks**
3. Click **Add New Webhook**

### Step 2: Configure Webhook Endpoint

Fill in the following details:

- **Name**: `NestJS Application Integration`
- **URL**: `https://your-domain.com/api/v1/absorb/webhooks`
- **Method**: `POST`
- **Content Type**: `application/json`
- **Secret**: Generate a secure secret key (save this for your environment variables)

### Step 3: Select Events

Choose the events you want to receive:

#### User Events
- ☑️ User Created
- ☑️ User Updated
- ☑️ User Deleted

#### Enrollment Events
- ☑️ Enrollment Created
- ☑️ Enrollment Completed
- ☑️ Enrollment Failed

#### Course Events
- ☑️ Course Created
- ☑️ Course Updated
- ☑️ Course Deleted

#### Certificate Events
- ☑️ Certificate Issued

#### Session Events
- ☑️ Session Created
- ☑️ Session Updated

### Step 4: Configure Headers (Optional)

Add custom headers if needed:
- `X-API-Key`: Your application's API key
- `X-Source`: `absorb-lms`

### Step 5: Test Configuration

1. Click **Test Webhook** to verify connectivity
2. Check your application logs for the test event
3. Verify the webhook verification endpoint responds correctly

## Environment Configuration

Add the following environment variables to your NestJS application:

```env
# Absorb LMS Configuration
ABSORB_API_BASE_URL=https://your-absorb-instance.myabsorb.com/api/rest/v2
ABSORB_API_KEY=your-api-key
ABSORB_USERNAME=your-username
ABSORB_PASSWORD=your-password
ABSORB_PRIVATE_KEY=your-private-key

# Webhook Configuration
ABSORB_WEBHOOK_SECRET=your-webhook-secret-from-step-2
ABSORB_WEBHOOK_ENABLE_SIGNATURE_VERIFICATION=true
ABSORB_WEBHOOK_ENABLE_TIMESTAMP_VALIDATION=true
```

## Webhook Event Examples

### User Created Event

```json
{
  "id": "wh_user_created_123456",
  "event": "user.created",
  "timestamp": "2024-12-20T10:30:00Z",
  "data": {
    "userId": 12345,
    "email": "<EMAIL>",
    "firstName": "John",
    "lastName": "Doe",
    "status": "Active"
  },
  "apiVersion": "2.0",
  "source": "absorb-lms"
}
```

### Enrollment Completed Event

```json
{
  "id": "wh_enrollment_completed_789012",
  "event": "enrollment.completed",
  "timestamp": "2024-12-20T15:45:00Z",
  "data": {
    "enrollmentId": 67890,
    "userId": 12345,
    "courseId": 98765,
    "status": "Completed",
    "completedAt": "2024-12-20T15:30:00Z",
    "score": 87.5
  },
  "apiVersion": "2.0",
  "source": "absorb-lms"
}
```

### Course Created Event

```json
{
  "id": "wh_course_created_345678",
  "event": "course.created",
  "timestamp": "2024-12-20T09:15:00Z",
  "data": {
    "courseId": 98765,
    "name": "Advanced Safety Training",
    "description": "Comprehensive safety training for senior staff",
    "status": "Published"
  },
  "apiVersion": "2.0",
  "source": "absorb-lms"
}
```

## Security Considerations

### Signature Verification

Absorb LMS signs webhook payloads using HMAC-SHA256. The signature is included in the `X-Absorb-Signature` header:

```
X-Absorb-Signature: sha256=a1b2c3d4e5f6...
```

Your application automatically verifies this signature using the webhook secret.

### Timestamp Validation

To prevent replay attacks, webhook events include timestamps. Events older than 5 minutes are rejected by default.

### HTTPS Requirement

Webhooks must be delivered over HTTPS to ensure data security in transit.

## Monitoring and Troubleshooting

### Health Check Endpoint

Monitor webhook endpoint health:
```
GET https://your-domain.com/api/v1/absorb/webhooks/health
```

Response:
```json
{
  "status": "healthy",
  "timestamp": "2024-12-20T10:30:00Z"
}
```

### Common Issues

1. **Webhook Verification Failed**
   - Check that your webhook secret matches the one configured in Absorb LMS
   - Ensure the signature verification is working correctly

2. **Events Not Being Received**
   - Verify your endpoint URL is accessible from the internet
   - Check firewall and security group settings
   - Ensure HTTPS is properly configured

3. **Signature Verification Errors**
   - Confirm the webhook secret is correctly set in environment variables
   - Check that the raw request body is being captured correctly

### Logging

The webhook service provides comprehensive logging:

```typescript
// Enable debug logging for detailed webhook processing information
LOGGER_LEVEL=debug
```

Log entries include:
- Incoming webhook events
- Signature verification results
- Event processing status
- Error details and stack traces

## Testing Webhooks Locally

For local development, use tools like ngrok to expose your local server:

```bash
# Install ngrok
npm install -g ngrok

# Expose local port 3000
ngrok http 3000

# Use the HTTPS URL provided by ngrok in your webhook configuration
# Example: https://abc123.ngrok.io/api/v1/absorb/webhooks
```

## Best Practices

1. **Idempotency**: Handle duplicate webhook deliveries gracefully
2. **Async Processing**: Process webhooks asynchronously for better performance
3. **Error Handling**: Implement proper error handling and retry logic
4. **Monitoring**: Set up monitoring and alerting for webhook failures
5. **Rate Limiting**: Implement rate limiting to handle webhook bursts
6. **Data Validation**: Always validate incoming webhook data
7. **Logging**: Log all webhook events for debugging and audit purposes

## Support

For issues related to:
- **Absorb LMS Configuration**: Contact Absorb LMS support
- **Application Integration**: Check application logs and error messages
- **Network Connectivity**: Verify firewall and DNS settings