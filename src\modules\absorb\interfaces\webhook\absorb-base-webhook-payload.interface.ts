import { ActionType, EntityType, WebhookEventType } from '../../enums';

/**
 * Base webhook payload interface (lean architecture)
 */
export interface AbsorbBaseWebhookPayload {
  // Event identification
  readonly eventType: WebhookEventType;
  readonly eventName: WebhookEventType; // Alias for eventType to match DTO
  readonly eventId?: string;
  readonly timestamp: string; // ISO 8601 datetime

  // Core entity references (minimal data)
  readonly userId?: string; // GUID - learner who triggered event or was affected
  readonly adminId?: string; // GUID - admin who performed action (for admin events)
  readonly courseId?: string; // GUID - course involved in event
  readonly enrollmentId?: string; // GUID - enrollment involved in event
  readonly departmentId?: string; // GUID - department context
  readonly groupId?: string; // GUID - group context

  // Transaction context
  readonly clientId?: string; // client identifier
  readonly correlationId?: string; // correlation ID for tracking

  // Event metadata
  readonly entityType?: EntityType;
  readonly action?: ActionType;

  // Additional context (minimal)
  readonly sessionId?: string; // GUID - for ILC events
  readonly competencyId?: string; // GUID - for competency events
  readonly certificateId?: string; // GUID - for certificate events
  readonly curriculumId?: string; // GUID - for curriculum events
  readonly chapterId?: string; // GUID - for chapter events
  readonly lessonId?: string; // GUID - for lesson events
}
