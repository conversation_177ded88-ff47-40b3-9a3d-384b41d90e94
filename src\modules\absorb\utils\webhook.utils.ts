import { AbsorbBaseWebhookPayload, AbsorbRawWebhookPayload } from '../interfaces/webhook';
import { WebhookEventType } from '../enums';

/**
 * Type guard to check if a payload is your standardized format
 */
export function isAbsorbBaseWebhookPayload(payload: unknown): payload is AbsorbBaseWebhookPayload {
  if (
    typeof payload === 'object' &&
    payload !== null &&
    typeof (payload as Record<string, unknown>)['eventType'] === 'string' &&
    typeof (payload as Record<string, unknown>)['timestamp'] === 'string' &&
    ((payload as Record<string, unknown>)['entityType'] === undefined ||
      typeof (payload as Record<string, unknown>)['entityType'] === 'string') &&
    ((payload as Record<string, unknown>)['action'] === undefined ||
      typeof (payload as Record<string, unknown>)['action'] === 'string')
  ) {
    return true;
  }
  return false;
}

/**
 * Type guard to check if a payload is raw Absorb format
 */
export function isAbsorbRawWebhookPayload(payload: unknown): payload is AbsorbRawWebhookPayload {
  return (
    typeof payload === 'object' &&
    payload !== null &&
    typeof (payload as Record<string, unknown>)['eventName'] === 'string' &&
    typeof (payload as Record<string, unknown>)['timestamp'] === 'string' &&
    typeof (payload as Record<string, unknown>)['details'] === 'object' &&
    (payload as Record<string, unknown>)['details'] !== null
  );
}

/**
 * Type guard to check if an event type is valid
 */
export function isValidWebhookEventType(eventName: string): eventName is WebhookEventType {
  return Object.values(WebhookEventType).includes(eventName as WebhookEventType);
}

/**
 * Safely parses JSON webhook payload
 */
export function parseWebhookPayload(body: string): AbsorbRawWebhookPayload | null {
  try {
    const parsed: unknown = JSON.parse(body);
    if (isAbsorbRawWebhookPayload(parsed)) {
      return parsed;
    }
    return null;
  } catch {
    return null;
  }
}

/**
 * Generates correlation ID for webhook processing
 */
export function generateCorrelationId(): string {
  return `webhook-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * Validates webhook timestamp for replay attack prevention
 */
export function isValidTimestamp(timestamp: string, toleranceSeconds = 300): boolean {
  try {
    const eventTime = new Date(timestamp).getTime();
    const currentTime = Date.now();
    const timeDiff = Math.abs(currentTime - eventTime) / 1000;

    return timeDiff <= toleranceSeconds;
  } catch {
    return false;
  }
}

/**
 * Extracts event type from raw Absorb event name
 */
export function extractEventType(eventName: string): WebhookEventType | null {
  // Handle exact matches first
  if (isValidWebhookEventType(eventName)) {
    return eventName;
  }

  // Handle common variations and mappings
  const eventMappings: Record<string, WebhookEventType> = {
    ['User Enrolled in Course by Admin']: WebhookEventType.USER_ENROLLED_IN_COURSE_BY_ADMIN,
    ['User Enrolled in Course by Learner']: WebhookEventType.USER_ENROLLED_IN_COURSE_BY_LEARNER,
    ['CertificateAwarded']: WebhookEventType.CERTIFICATE_AWARDED,
  };

  return eventMappings[eventName] ?? null;
}
