/**
 * Result of webhook signature verification
 */
export interface AbsorbWebhookSignatureVerification {
  /** Whether the signature is valid */
  readonly isValid: boolean;

  /** Computed signature for comparison */
  readonly computedSignature: string;

  /** Received signature from headers */
  readonly receivedSignature: string;

  /** Error message if verification failed */
  readonly error?: string;
}
