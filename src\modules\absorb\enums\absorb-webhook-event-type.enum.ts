/**
 * Absorb webhook event types based on official AbsorbLMS documentation
 * https://support.absorblms.com/hc/en-us/articles/28999993938451-Webhooks
 */
/**
 * Enum for Absorb LMS webhook event types
 * Based on official Absorb LMS webhook documentation
 */
export enum WebhookEventType {
  // User Events
  USER_CREATED = 'UserCreated',
  USER_UPDATED = 'UserUpdated',
  USER_DELETED = 'UserDeleted',
  USER_ACTIVATED = 'UserActivated',
  USER_DEACTIVATED = 'UserDeactivated',

  // Course Events
  COURSE_CREATED = 'CourseCreated',
  COURSE_UPDATED = 'CourseUpdated',
  COURSE_DELETED = 'CourseDeleted',
  COURSE_PUBLISHED = 'CoursePublished',
  COURSE_UNPUBLISHED = 'CourseUnpublished',
  ONLINE_COURSE_ACTIVATED = 'OnlineCourseActivated',
  ILC_ACTIVATED = 'ILCActivated',
  ONLINE_COURSE_DEACTIVATED = 'OnlineCourseDeactivated',
  ILC_DEACTIVATED = 'ILCDeactivated',

  // Enrollment Events
  USER_ENROLLED_IN_COURSE_BY_ADMIN = 'User Enrolled in Course by Admin',
  USER_ENROLLED_IN_COURSE_BY_LEARNER = 'User Enrolled in Course by Learner',
  USER_UNENROLLED_FROM_COURSE = 'User Unenrolled from Course',

  // Completion Events
  COURSE_COMPLETED = 'CourseCompleted',
  LESSON_COMPLETED = 'LessonCompleted',
  CHAPTER_COMPLETED = 'ChapterCompleted',

  // Assessment Events
  ASSESSMENT_STARTED = 'AssessmentStarted',
  ASSESSMENT_COMPLETED = 'AssessmentCompleted',
  ASSESSMENT_PASSED = 'AssessmentPassed',
  ASSESSMENT_FAILED = 'AssessmentFailed',

  // Certificate Events
  CERTIFICATE_AWARDED = 'CertificateAwarded',
  CERTIFICATE_REVOKED = 'CertificateRevoked',
  CERTIFICATE_EXPIRED = 'CertificateExpired',

  // Group Events
  GROUP_CREATED = 'GroupCreated',
  GROUP_UPDATED = 'GroupUpdated',
  GROUP_DELETED = 'GroupDeleted',
  USER_ADDED_TO_GROUP = 'UserAddedToGroup',
  USER_REMOVED_FROM_GROUP = 'UserRemovedFromGroup',

  // Department Events
  DEPARTMENT_CREATED = 'DepartmentCreated',
  DEPARTMENT_UPDATED = 'DepartmentUpdated',
  DEPARTMENT_DELETED = 'DepartmentDeleted',

  // Session Events
  SESSION_STARTED = 'SessionStarted',
  SESSION_COMPLETED = 'SessionCompleted',
  SESSION_CANCELLED = 'SessionCancelled',

  // Custom Events
  CUSTOM_EVENT = 'CustomEvent',

  // Survey Events
  SURVEY_COMPLETED = 'SurveyCompleted',

  //Learning Path
  LEARNING_PATH_COMPLETED = 'LearningPathCompleted',

  // Test Events
  TEST_EVENT = 'Test Event',
}

/**
 * Helper function to check if an event type is valid
 */
export function isValidWebhookEventType(eventName: string): eventName is WebhookEventType {
  return Object.values(WebhookEventType).includes(eventName as WebhookEventType);
}

/**
 * Helper function to get event category
 */
export function getEventCategory(eventType: WebhookEventType): string {
  if (eventType.includes('User') && !eventType.includes('Course') && !eventType.includes('Group')) {
    return 'user';
  }
  if (eventType.includes('Course')) {
    return 'course';
  }
  if (eventType.includes('Enroll')) {
    return 'enrollment';
  }
  if (eventType.includes('Completed') || eventType.includes('Completion')) {
    return 'completion';
  }
  if (eventType.includes('Assessment')) {
    return 'assessment';
  }
  if (eventType.includes('Certificate')) {
    return 'certificate';
  }
  if (eventType.includes('Group')) {
    return 'group';
  }
  if (eventType.includes('Department')) {
    return 'department';
  }
  if (eventType.includes('Session')) {
    return 'session';
  }
  return 'other';
}
