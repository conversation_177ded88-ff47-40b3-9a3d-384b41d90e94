import {
  CallHand<PERSON>,
  ExecutionContext,
  HttpException,
  Injectable,
  NestInterceptor,
} from '@nestjs/common';
import { Observable, throwError } from 'rxjs';
import { catchError, tap } from 'rxjs/operators';
import { Response } from 'express';
import { LoggerService } from './logger.service';
import { RequestWithCorrelation } from './correlation-id.middleware';

@Injectable()
export class LoggerInterceptor implements NestInterceptor {
  constructor(private readonly _logger: LoggerService) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<unknown> {
    const { request, response, startTime } = this._extractRequestData(context);
    this._logRequest(request, startTime);

    return next.handle().pipe(
      tap(data => this._logSuccessResponse(request, response, data, startTime)),
      catchError(error => this._logErrorResponse(request, error, startTime)),
    );
  }

  private _extractRequestData(context: ExecutionContext): {
    request: RequestWithCorrelation;
    response: Response;
    startTime: number;
  } {
    const ctx = context.switchToHttp();
    return {
      request: ctx.getRequest<RequestWithCorrelation>(),
      response: ctx.getResponse<Response>(),
      startTime: Date.now(),
    };
  }

  private _logRequest(request: RequestWithCorrelation, _startTime: number): void {
    const method = request.method;
    const url = request.url;
    const body = request.body as unknown;
    const query = request.query as Record<string, unknown>;
    const params = request.params as Record<string, unknown>;
    const headers = request.headers as Record<string, string | string[]>;
    const correlationId = request.correlationId;
    const userAgent = (headers['user-agent'] as string) ?? 'Unknown';
    const ip = request.ip ?? request.connection.remoteAddress ?? 'Unknown';

    this._logger.setLogContext({
      correlationId,
      method,
      url,
      userAgent,
      ip,
    });

    this._logger.logStructured(
      'log',
      'HTTP Request',
      {
        method,
        url,
        body: this._sanitizeBody(body),
        query,
        params,
        userAgent,
        ip,
      },
      'LoggerInterceptor',
    );
  }

  private _logSuccessResponse(
    request: RequestWithCorrelation,
    response: Response,
    data: unknown,
    startTime: number,
  ): void {
    const duration = Date.now() - startTime;
    const statusCode = response.statusCode;
    const { method, url } = request;

    this._logger.logApiRequest({ method, url, statusCode, duration }, 'LoggerInterceptor');
    this._logger.logStructured(
      'debug',
      'HTTP Response',
      {
        statusCode,
        duration,
        responseSize: JSON.stringify(data).length,
      },
      'LoggerInterceptor',
    );
  }

  private _logErrorResponse(
    request: RequestWithCorrelation,
    error: unknown,
    startTime: number,
  ): Observable<never> {
    const duration = Date.now() - startTime;
    const statusCode = error instanceof HttpException ? error.getStatus() : 500;
    const { method, url } = request;

    this._logger.logApiRequest({ method, url, statusCode, duration }, 'LoggerInterceptor');

    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    const errorStack = error instanceof Error ? error.stack : undefined;

    this._logger.logStructured(
      'error',
      'HTTP Request Error',
      {
        statusCode,
        duration,
        error: errorMessage,
        stack: errorStack,
      },
      'LoggerInterceptor',
    );

    return throwError(() => error);
  }

  /**
   * Sanitize request body to remove sensitive information
   */
  private _sanitizeBody(body: unknown): unknown {
    if (!body || typeof body !== 'object') {
      return body;
    }

    const sensitiveFields = ['password', 'token', 'secret', 'authorization', 'apiKey'];
    const sanitized = { ...(body as Record<string, unknown>) };

    for (const field of sensitiveFields) {
      if (sanitized[field]) {
        sanitized[field] = '[REDACTED]';
      }
    }

    return sanitized;
  }
}
