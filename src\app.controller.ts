import { Controller, Get } from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';

@ApiTags('app')
@Controller()
export class AppController {
  @Get()
  @ApiOperation({ summary: 'Health check endpoint' })
  @ApiResponse({ status: 200, description: 'Application is running' })
  getHello(): string {
    return 'Hello World!';
  }

  @Get('/debug-sentry')
  @ApiOperation({ summary: 'Test Sentry error tracking' })
  @ApiResponse({ status: 500, description: 'Intentional error for Sentry testing' })
  getError(): never {
    throw new Error('My first Sentry error!');
  }
}
