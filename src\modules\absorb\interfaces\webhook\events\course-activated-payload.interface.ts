import { WebhookEventType } from '@/modules/absorb/enums';
import { AbsorbBaseWebhookPayload } from '../absorb-base-webhook-payload.interface';

/**
 * Course management payloads
 */
export interface CourseActivatedPayload extends AbsorbBaseWebhookPayload {
  readonly eventType: WebhookEventType.ONLINE_COURSE_ACTIVATED | WebhookEventType.ILC_ACTIVATED;
  readonly courseName: string;
  readonly activationDate: string; // ISO 8601 datetime
}
