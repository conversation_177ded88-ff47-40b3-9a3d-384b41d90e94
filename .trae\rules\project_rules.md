# PsychScene Bridge - AI Coding Rules

## Framework & Technology Stack

- **Framework**: NestJS version ^11
- **Language**: TypeScript with strict mode enabled
- **Node.js**: ES2023 target with nodenext module resolution
- **Testing**: Jest with 80% minimum coverage requirement
- **Documentation**: Swagger/OpenAPI integration

## Project Architecture

- **Modular Architecture**: Feature-based modules in `src/modules/`
- **Dependency Injection**: Use NestJS DI container exclusively
- **Layered Structure**: Controller → Service → Repository pattern
- **Shared Resources**: Common utilities in `src/common/` and `src/shared/`
- **Configuration**: Environment-based config in `src/config/`

## Folder Structure Rules

```
src/
├── app.module.ts              # Root module
├── main.ts                    # Entry point
├── common/                    # Shared utilities
│   ├── decorators/           # Custom decorators
│   ├── filters/              # Exception filters
│   ├── guards/               # Auth guards
│   ├── interceptors/         # Request/response interceptors
│   ├── pipes/                # Validation pipes
│   └── utils/                # Utility functions
├── config/                    # Configuration files
├── modules/                   # Feature modules
│   └── [feature]/            # Each feature module
│       ├── [feature].controller.ts
│       ├── [feature].service.ts
│       ├── [feature].module.ts
│       ├── dto/              # Data Transfer Objects
│       └── entities/         # Database entities
├── shared/                    # Shared services
│   ├── auth/
│   ├── cache/
│   ├── database/
│   ├── email/
│   ├── logger/
│   └── validation/
└── types/                     # Type definitions
```

## Coding Standards

### File Naming Conventions

- **Files**: kebab-case (e.g., `user-profile.service.ts`)
- **Directories**: kebab-case (e.g., `user-management/`)
- **Test files**: `.spec.ts` suffix (e.g., `user.service.spec.ts`)
- **E2E tests**: `.e2e-spec.ts` suffix

### TypeScript Naming Conventions

- **Classes**: PascalCase (e.g., `UserService`, `AuthController`)
- **Interfaces**: PascalCase with 'I' prefix (e.g., `IUserRepository`)
- **Types**: PascalCase (e.g., `UserRole`, `ApiResponse`)
- **Enums**: PascalCase (e.g., `UserStatus`)
- **Variables/Functions**: camelCase (e.g., `userName`, `getUserById`)
- **Constants**: SCREAMING_SNAKE_CASE (e.g., `MAX_RETRY_ATTEMPTS`)
- **Private members**: Leading underscore (e.g., `_privateMethod`)

### NestJS File Conventions

- **Controllers**: `*.controller.ts`
- **Services**: `*.service.ts`
- **Modules**: `*.module.ts`
- **DTOs**: `*.dto.ts`
- **Entities**: `*.entity.ts`
- **Guards**: `*.guard.ts`
- **Pipes**: `*.pipe.ts`
- **Interceptors**: `*.interceptor.ts`

## Code Quality Requirements

### TypeScript Strict Rules

- **Strict mode**: Enabled with all strict options
- **No implicit any**: Required
- **Explicit return types**: Required for functions
- **No unused variables/parameters**: Enforced
- **Exact optional properties**: Enabled

### Code Complexity Limits

- **Cyclomatic complexity**: Maximum 10
- **Function length**: Maximum 50 lines
- **File length**: Maximum 300 lines
- **Function parameters**: Maximum 4
- **Nesting depth**: Maximum 4 levels

### Code Coverage

- **Minimum coverage**: 80% for all metrics
- **Branches**: 80%
- **Functions**: 80%
- **Lines**: 80%
- **Statements**: 80%

## Formatting Rules (Prettier)

- **Print width**: 100 characters
- **Indentation**: 2 spaces (no tabs)
- **Quotes**: Single quotes
- **Semicolons**: Always required
- **Trailing commas**: All
- **Bracket spacing**: Enabled
- **Arrow function parentheses**: Avoid when possible

## Import/Export Rules

- **Path aliases**: Use `@/` for src root, `@/common/*`, `@/modules/*`, etc.
- **Import sorting**: Alphabetical with separated groups
- **No duplicate imports**: Enforced
- **Prefer named exports**: Over default exports

## API Development Standards

- **RESTful design**: Follow REST principles
- **Versioning**: Use `/api/v1/` prefix
- **Swagger documentation**: Required for all endpoints
- **DTO validation**: Use class-validator for all inputs
- **Error handling**: Consistent error response format
- **Response format**: Standardized JSON structure

## Testing Requirements

- **Unit tests**: Required for all services and utilities
- **Integration tests**: Required for controllers
- **E2E tests**: Required for critical user flows
- **Test coverage**: Minimum 80% across all metrics
- **Test naming**: Descriptive test names with "should" statements
- **Test structure**: Arrange-Act-Assert pattern

## Documentation Standards

- **JSDoc**: Required for all public methods and classes
- **Parameter descriptions**: Include types and descriptions
- **Return type documentation**: Required
- **Complex logic**: Inline comments for business logic
- **API documentation**: Swagger decorators for all endpoints
- **README updates**: Keep setup and usage instructions current

## Security & Best Practices

- **Environment variables**: Never commit sensitive data
- **Input validation**: Validate all user inputs with DTOs
- **Error handling**: Don't expose internal errors to clients
- **Authentication**: Use guards for protected routes
- **Authorization**: Implement role-based access control
- **Logging**: Use structured logging for debugging

## Development Workflow

1. **Before coding**: Run `npm run quality:check`
2. **During development**: Use `npm run start:dev` for hot reload
3. **Before committing**: Run `npm run quality:fix` and ensure tests pass
4. **Pre-commit hooks**: Automatically run linting, formatting, and tests

## Module Development Guidelines

1. **Single responsibility**: Each module has one clear purpose
2. **Dependency injection**: Use NestJS DI for all dependencies
3. **Error handling**: Implement custom exception filters
4. **Validation**: Use DTOs with class-validator
5. **Testing**: Maintain 80% coverage minimum
6. **Documentation**: Document all public APIs

## Performance Considerations

- **Lazy loading**: Use dynamic imports for large modules
- **Caching**: Implement caching for expensive operations
- **Database queries**: Optimize with proper indexing
- **Memory management**: Avoid memory leaks in long-running processes
- **Bundle size**: Monitor and optimize application size

## Documentation Reference Guidelines

### Official Documentation Sources

1. **NestJS Framework**: Always consult the official NestJS documentation at `https://docs.nestjs.com/` for all framework-related matters
2. **TypeScript**: Reference TypeScript for JavaScript Programmers at `https://www.typescriptlang.org/docs/handbook/typescript-in-5-minutes.html` for TypeScript-specific guidance
3. **Absorb Integration API**: Use Absorb Integration API v2 documentation at `https://docs.myabsorb.com/docs/v2/integration-api` for integration requirements

### Documentation Research Protocol

- **Combined Review**: When TypeScript updates are required, review both TypeScript and NestJS documentation together to ensure compatibility
- **Topic-Specific Research**: Documentation research should be topic-specific - verify all relevant documentation before implementation
- **Implementation Validation**: All development decisions must be properly informed by official documentation sources

### Approved Documentation Access Methods

1. **Trae IDE Context**: Utilize Trae IDE's document context when available in the indexed documentation
2. **MCP Context**: Use MCP Context for accessing documentation resources
3. **MCP Fetch**: Fetch documentation through MCP when direct access is needed

### Documentation Compliance

- **Verification Required**: Always verify implementation approaches against official documentation
- **Version Compatibility**: Ensure all documentation references match the project's technology stack versions
- **Cross-Reference**: When working with multiple technologies, cross-reference documentation to avoid conflicts

## AI Coder Specific Instructions

- **Follow existing patterns**: Maintain consistency with existing code
- **Use project utilities**: Leverage existing common utilities
- **Respect boundaries**: Don't modify core framework files
- **Test thoroughly**: Write comprehensive tests for new features
- **Document changes**: Update relevant documentation
- **Security first**: Always consider security implications
- **Performance aware**: Consider performance impact of changes
- **Documentation-driven**: Always consult official documentation before implementation
