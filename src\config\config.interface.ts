import { IAbsorbLmsConfig } from '@/modules/absorb/config';

/**
 * Configuration interface for the application
 */
export interface IAppConfig {
  nodeEnv: string;
  port: number;
  apiPrefix: string;
}

/**
 * Database configuration interface
 */
export interface IDatabaseConfig {
  url: string;
  host: string;
  port: number;
  name: string;
  username: string;
  password: string;
}

/**
 * JWT configuration interface
 */
export interface IJwtConfig {
  secret: string;
  expiresIn: string;
}

/**
 * CORS configuration interface
 */
export interface ICorsConfig {
  origin: string | string[];
}

/**
 * Logging configuration interface
 */
export interface ILoggingConfig {
  level: string;
  prettyPrint: boolean;
  colorize: boolean;
  translateTime: boolean;
  ignore: string;
  redact: string[];
}

/**
 * Rate limiting configuration interface
 */
export interface IThrottleConfig {
  ttl: number;
  limit: number;
}

/**
 * Feature flags configuration interface
 */
export interface IFeatureConfig {
  enableSwagger: boolean;
  enableDebug: boolean;
}

/**
 * Sentry configuration interface
 */
export interface ISentryConfig {
  dsn: string;
  environment: string;
  enabled: boolean;
  tracesSampleRate: number;
  profilesSampleRate: number;
  enableLogs: boolean;
}

/**
 * Main configuration interface that combines all config sections
 */
export interface IConfig {
  app: IAppConfig;
  database: IDatabaseConfig;
  jwt: IJwtConfig;
  cors: ICorsConfig;
  logging: ILoggingConfig;
  throttle: IThrottleConfig;
  features: IFeatureConfig;
  sentry: ISentryConfig;
  absorb: IAbsorbLmsConfig;
}
