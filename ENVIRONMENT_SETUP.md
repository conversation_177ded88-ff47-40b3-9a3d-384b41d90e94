# Environment Setup Guide

## Overview

This project uses environment-specific configuration files to manage different deployment environments. For security reasons, actual environment files containing sensitive data are **not committed** to the repository.

## Environment Files Structure

```
.env                    # Default fallback values (committed)
.env.local              # Local overrides (not committed)
.env.development        # Development config (not committed)
.env.staging            # Staging config (not committed)
.env.production         # Production config (not committed)
.env.*.example          # Template files (committed)
```

## Setup Instructions

### 1. Development Environment

```bash
# Copy the development template
cp .env.development.example .env.development

# Edit the file with your actual values
nano .env.development  # or use your preferred editor
```

### 2. Staging Environment

```bash
# Copy the staging template
cp .env.staging.example .env.staging

# Edit the file with your actual values
nano .env.staging
```

### 3. Production Environment

```bash
# Copy the production template
cp .env.production.example .env.production

# Edit the file with your actual values
nano .env.production
```

## Security Best Practices

### ✅ What IS committed to the repository:

- `.env` - Default/fallback values (non-sensitive)
- `.env.*.example` - Template files with placeholder values
- This documentation

### ❌ What is NOT committed to the repository:

- `.env.development` - Contains actual development credentials
- `.env.staging` - Contains actual staging credentials
- `.env.production` - Contains actual production credentials
- `.env.local` - Local overrides
- `.env.*.local` - Environment-specific local overrides

## Environment Loading Priority

The application loads environment variables in this order (higher priority overrides lower):

1. `.env.local` (highest priority)
2. `.env.${NODE_ENV}` (e.g., `.env.development`)
3. `.env` (lowest priority)

## Required Variables

Each environment file must contain these variables:

### Application

- `NODE_ENV` - Environment name (development/staging/production)
- `PORT` - Application port
- `API_PREFIX` - API route prefix

### Database

- `DATABASE_URL` - Full database connection string
- `DATABASE_HOST` - Database host
- `DATABASE_PORT` - Database port
- `DATABASE_NAME` - Database name
- `DATABASE_USERNAME` - Database username
- `DATABASE_PASSWORD` - Database password

### JWT

- `JWT_SECRET` - JWT signing secret (minimum 32 characters)
- `JWT_EXPIRES_IN` - Token expiration time

### CORS

- `CORS_ORIGIN` - Allowed origins (comma-separated)

### Logging

- `LOG_LEVEL` - Logging level (error/warn/info/debug/verbose)

### Rate Limiting

- `THROTTLE_TTL` - Rate limit time window (seconds)
- `THROTTLE_LIMIT` - Maximum requests per window

### Feature Flags

- `ENABLE_SWAGGER` - Enable Swagger documentation
- `ENABLE_DEBUG` - Enable debug mode

## Deployment Notes

### Development

- Use `.env.development` for local development
- Enable Swagger and debug features
- Use relaxed security settings

### Staging

- Use `.env.staging` for staging deployments
- Mirror production settings but with staging resources
- Enable Swagger for testing

### Production

- Use `.env.production` for production deployments
- Disable Swagger and debug features
- Use strict security settings
- Ensure all secrets are properly secured

## Troubleshooting

### Missing Environment File

If you get validation errors on startup, ensure you've copied and configured the appropriate `.env.*` file for your environment.

### Invalid Configuration

The application uses Joi validation to ensure all required environment variables are present and valid. Check the console output for specific validation errors.

### Environment Not Loading

Verify that `NODE_ENV` is set correctly and matches your environment file name (without the `.env.` prefix).
