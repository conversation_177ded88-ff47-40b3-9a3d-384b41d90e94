import { WebhookEventType } from '../../enums';

/**
 * Interface for standardized webhook response
 * Provides consistent response format for all webhook processing results
 */
export interface AbsorbWebhookResponse {
  /** Whether processing was successful */
  readonly success: boolean;

  /** Processing duration in milliseconds */
  readonly duration: number;

  /** Correlation ID */
  readonly correlationId: string;

  /** Event type processed */
  readonly eventType: WebhookEventType;

  /** Error information if processing failed */
  readonly error?: {
    readonly code: string;
    readonly message: string;
    readonly details?: any;
  };

  /** Response data */
  readonly data?: any;
}
