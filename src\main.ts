// Import Sentry instrumentation first!
import './instrument';

import { NestFactory } from '@nestjs/core';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { AppModule } from './app.module';
import { ConfigService } from './config';
import { LoggerService } from './shared/logger';

async function bootstrap(): Promise<void> {
  const app = await NestFactory.create(AppModule, {
    bufferLogs: true,
  });

  // Get configuration service
  const configService = app.get(ConfigService);
  const appConfig = configService.app;
  const corsConfig = configService.cors;
  const featuresConfig = configService.features;
  const loggingConfig = configService.logging;

  // Setup custom logger
  const customLogger = app.get(LoggerService);
  app.useLogger(customLogger);

  // Set global prefix for all routes
  app.setGlobalPrefix(appConfig.apiPrefix);

  // Configure CORS
  app.enableCors({
    origin: corsConfig.origin,
    credentials: true,
  });

  // Setup Swagger documentation only if enabled
  if (featuresConfig.enableSwagger) {
    const config = new DocumentBuilder()
      .setTitle('PsychScene Bridge API')
      .setDescription('The PsychScene Bridge API description')
      .setVersion('1.0')
      .addTag('psychscene')
      .addServer(`http://localhost:${appConfig.port}`, 'Development server')
      .build();
    const documentFactory = (): ReturnType<typeof SwaggerModule.createDocument> =>
      SwaggerModule.createDocument(app, config);
    SwaggerModule.setup('docs', app, documentFactory);
  }

  await app.listen(appConfig.port);

  // Log application startup information
  const appUrl = `http://localhost:${appConfig.port}`;

  customLogger.setLogContext({ service: 'Bootstrap' });
  customLogger.logBusinessEvent(
    'application_started',
    {
      url: appUrl,
      environment: appConfig.nodeEnv,
      logLevel: loggingConfig.level,
      prettyPrint: loggingConfig.prettyPrint,
    },
    'Bootstrap',
  );

  customLogger.log(`Application is running on: ${appUrl}`, 'Bootstrap');
  customLogger.log(`Environment: ${appConfig.nodeEnv}`, 'Bootstrap');
  customLogger.log(`Log Level: ${loggingConfig.level}`, 'Bootstrap');

  if (featuresConfig.enableSwagger) {
    customLogger.log(`Swagger documentation: ${appUrl}/docs`, 'Bootstrap');
  }
}
void bootstrap();
