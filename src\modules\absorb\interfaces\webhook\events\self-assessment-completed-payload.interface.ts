import { WebhookEventType } from '@/modules/absorb/enums';
import { AbsorbBaseWebhookPayload } from '../absorb-base-webhook-payload.interface';

/**
 * Skills subscription specific events (requires Skills subscription)
 */
export interface SelfAssessmentCompletedPayload extends AbsorbBaseWebhookPayload {
  readonly eventType: WebhookEventType.SELF_ASSESSMENT_COMPLETED;
  readonly assessmentId: string; // GUID - self-assessment identifier
  readonly skillIds?: string[]; // Array of GUID skill identifiers
}
