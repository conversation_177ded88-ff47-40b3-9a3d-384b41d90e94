import { Injectable, Logger } from '@nestjs/common';
import { AbsorbWebhookEventHandlersService } from './absorb-webhook-event-handlers.service';
import { WebhookProcessingContext } from '../../interfaces/webhook/absorb-webhook-processing-context.interface';

/**
 * Webhook Processing Result
 * Result of webhook event processing
 */
export interface WebhookProcessingResult {
  success: boolean;
  message: string;
  eventType?: string;
  processingTimeMs?: number;
  error?: string;
}

/**
 * AbsorbLMS Webhook Service
 * Handles webhook reception, signature verification, and event processing
 *
 * Features:
 * - Secure webhook processing with signature verification
 * - Event routing and handling
 * - Comprehensive logging and error handling
 * - Integration with AbsorbLMS Integration API for data enrichment
 */
@Injectable()
export class AbsorbWebhookService {
  private readonly logger = new Logger(AbsorbWebhookService.name);

  constructor(private readonly eventHandlers: AbsorbWebhookEventHandlersService) {}

  /**
   * Process incoming webhook event with comprehensive security validation
   *
   * @param context - Webhook processing context with headers, payload, and security info
   * @returns Processing result
   */
  public async processWebhookEvent(
    context: WebhookProcessingContext,
  ): Promise<WebhookProcessingResult> {
    const startTime = Date.now();

    try {
      this.logger.log('Processing webhook event', {
        correlationId: context.correlationId,
        eventName: context.payload?.eventName,
        hasHeaders: !!context.headers,
        clientIp: context.request?.ip,
        userAgent: context.headers?.userAgent,
      });

      // Validate payload structure
      this.validatePayloadStructure(context.payload);

      // Route to appropriate event handler
      await this.routeEvent(context);

      const processingTime = Date.now() - startTime;

      this.logger.log('Webhook event processed successfully', {
        correlationId: context.correlationId,
        eventName: context.payload?.eventName,
        processingTimeMs: processingTime,
      });

      return {
        success: true,
        message: 'Webhook processed successfully',
        eventType: context.payload?.eventName,
        processingTimeMs: processingTime,
      };
    } catch (error) {
      const processingTime = Date.now() - startTime;

      this.logger.error('Webhook processing failed', {
        correlationId: context.correlationId,
        eventName: context.payload?.eventName,
        error: error.message,
        stack: error.stack,
        processingTimeMs: processingTime,
      });

      return {
        success: false,
        message: 'Webhook processing failed',
        eventType: context.payload?.eventName,
        processingTimeMs: processingTime,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Validate the structure of the webhook payload
   */
  private validatePayloadStructure(payload: {
    eventName?: string;
    transactionInfo?: unknown;
  }): void {
    if (!payload) {
      throw new Error('Payload is required');
    }

    if (!payload.eventName) {
      throw new Error('Event name is required in payload');
    }

    if (!payload.transactionInfo) {
      throw new Error('Transaction info is required in payload');
    }

    this.logger.debug('Payload structure validation passed', {
      eventName: payload.eventName,
      hasTransactionInfo: !!payload.transactionInfo,
    });
  }

  /**
   * Route the event to the appropriate handler
   */
  private async routeEvent(context: WebhookProcessingContext): Promise<void> {
    this.logger.debug('Routing webhook event', {
      eventType: context.payload.eventName,
      correlationId: context.correlationId,
    });

    await this.eventHandlers.routeEvent(context);
  }

  /**
   * Validate webhook payload structure
   */
  private validatePayload(payload: any): void {
    if (!payload) {
      throw new Error('Payload is required');
    }

    if (!payload.eventName) {
      throw new Error('Event name is required');
    }

    this.logger.debug('Payload validation passed', {
      eventName: payload.eventName,
      hasData: !!payload.data,
    });
  }

  /**
   * Process webhook with updated interface
   */
  public async processWebhook(
    context: WebhookProcessingContext,
  ): Promise<{ success: boolean; message: string; eventType?: string }> {
    console.log('Processing webhook event context', context);
    try {
      const result = await this.processWebhookEvent(context);
      return {
        success: result.success,
        message: result.message,
        ...(result.eventType && { eventType: result.eventType }),
      };
    } catch (error) {
      this.logger.error('Failed to process webhook', {
        correlationId: context.correlationId,
        error: error.message,
        stack: error.stack,
      });

      return {
        success: false,
        message: 'Failed to process webhook',
      };
    }
  }
}
