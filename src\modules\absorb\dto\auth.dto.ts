import { IsNotEmpty, <PERSON>Optional, IsString, Min<PERSON>ength } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO for Absorb authentication credentials
 * All fields are optional - if not provided, values from environment configuration will be used
 */
export class AbsorbAuthCredentialsDto {
  @ApiProperty({
    description: 'Absorb LMS username (optional - uses environment config if not provided)',
    example: '<EMAIL>',
    required: false,
  })
  @IsOptional()
  @IsString()
  @IsNotEmpty()
  username?: string;

  @ApiProperty({
    description: 'Absorb LMS password (optional - uses environment config if not provided)',
    example: 'securePassword123',
    required: false,
  })
  @IsOptional()
  @IsString()
  @IsNotEmpty()
  @MinLength(6)
  password?: string;

  @ApiProperty({
    description:
      'Absorb LMS private key for API access (optional - uses environment config if not provided)',
    example: 'your-private-key-here',
    required: false,
  })
  @IsOptional()
  @IsString()
  @IsNotEmpty()
  privateKey?: string;
}

/**
 * DTO for authentication response
 */
export class AbsorbAuthResponseDto {
  @ApiProperty({
    description: 'Access token for API requests',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
  })
  token: string;

  @ApiProperty({
    description: 'Token expiration timestamp',
    example: '2024-12-20T10:30:00Z',
  })
  expiresAt: string;

  @ApiProperty({
    description: 'Token type',
    example: 'Bearer',
  })
  tokenType: string;
}

/**
 * DTO for authentication status response
 */
export class AbsorbAuthStatusDto {
  @ApiProperty({
    description: 'Whether the user is currently authenticated',
    example: true,
  })
  isAuthenticated: boolean;

  @ApiProperty({
    description: 'Token expiration timestamp',
    example: '2024-12-20T10:30:00Z',
    required: false,
  })
  expiresAt?: string;

  @ApiProperty({
    description: 'Seconds until token expires',
    example: 3600,
    required: false,
  })
  timeUntilExpiry?: number;
}

/**
 * DTO for logout response
 */
export class AbsorbLogoutResponseDto {
  @ApiProperty({
    description: 'Logout confirmation message',
    example: 'Successfully logged out',
  })
  message: string;
}
