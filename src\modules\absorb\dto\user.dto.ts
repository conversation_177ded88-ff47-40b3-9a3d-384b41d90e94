import { IsEnum, IsOptional, IsString } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { ActiveStatusEnum, GenderEnum } from '../enums';

/**
 * User creation DTO
 */
export class CreateUserDto {
  @ApiProperty({
    description: 'Department ID (GUID)',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsString()
  departmentId: string;

  @ApiProperty({ description: 'First name (max 255 chars)', example: '<PERSON>' })
  @IsString()
  firstName: string;

  @ApiProperty({ description: 'Last name (max 255 chars)', example: 'Doe' })
  @IsString()
  lastName: string;

  @ApiProperty({ description: 'Username (max 255 chars, unique)', example: 'john.doe' })
  @IsString()
  username: string;

  @ApiProperty({ description: 'Middle name', example: '<PERSON>', required: false })
  @IsOptional()
  @IsString()
  middleName?: string;

  @ApiProperty({ description: 'Email address', example: '<EMAIL>', required: false })
  @IsOptional()
  @IsString()
  emailAddress?: string;

  @ApiProperty({ description: 'External system ID', example: 'EXT001', required: false })
  @IsOptional()
  @IsString()
  externalId?: string;
}

/**
 * User update DTO
 */
export class UpdateUserDto {
  @ApiProperty({ description: 'First name', example: 'John', required: false })
  @IsOptional()
  @IsString()
  firstName?: string;

  @ApiProperty({ description: 'Last name', example: 'Doe', required: false })
  @IsOptional()
  @IsString()
  lastName?: string;

  @ApiProperty({ description: 'Email address', example: '<EMAIL>', required: false })
  @IsOptional()
  @IsString()
  emailAddress?: string;

  @ApiProperty({ description: 'Phone number', example: '******-123-4567', required: false })
  @IsOptional()
  @IsString()
  phone?: string;

  @ApiProperty({ description: 'Job title', example: 'Software Engineer', required: false })
  @IsOptional()
  @IsString()
  jobTitle?: string;

  @ApiProperty({ description: 'Active status', enum: ActiveStatusEnum, required: false })
  @IsOptional()
  @IsEnum(ActiveStatusEnum)
  activeStatus?: ActiveStatusEnum;

  @ApiProperty({ description: 'Gender', enum: GenderEnum, required: false })
  @IsOptional()
  @IsEnum(GenderEnum)
  gender?: GenderEnum;
}

/**
 * User response DTO
 */
export class UserResponseDto {
  @ApiProperty({ description: 'User ID (GUID)', example: '550e8400-e29b-41d4-a716-************' })
  id: string;

  @ApiProperty({ description: 'Department ID (GUID)' })
  departmentId: string;

  @ApiProperty({ description: 'First name' })
  firstName: string;

  @ApiProperty({ description: 'Last name' })
  lastName: string;

  @ApiProperty({ description: 'Username' })
  username: string;

  @ApiProperty({ description: 'Email address', required: false })
  @IsOptional()
  emailAddress?: string;

  @ApiProperty({ description: 'Active status', enum: ActiveStatusEnum })
  activeStatus: ActiveStatusEnum;

  @ApiProperty({ description: 'Date added (ISO 8601)', example: '2024-01-01T00:00:00Z' })
  dateAdded: string;

  @ApiProperty({ description: 'Date last edited (ISO 8601)', example: '2024-01-01T00:00:00Z' })
  dateEdited: string;

  @ApiProperty({ description: 'Is learner flag' })
  isLearner: boolean;

  @ApiProperty({ description: 'Is admin flag' })
  isAdmin: boolean;

  @ApiProperty({ description: 'Is instructor flag' })
  isInstructor: boolean;

  @ApiProperty({ description: 'Is manager flag' })
  isManager: boolean;
}
