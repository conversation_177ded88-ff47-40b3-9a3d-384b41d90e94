import {
  BadRequestException,
  CanActivate,
  ExecutionContext,
  Injectable,
  Logger,
  UnauthorizedException,
} from '@nestjs/common';
import { Request } from 'express';
import { validate } from 'class-validator';
import { plainToClass } from 'class-transformer';
import {
  AbsorbWebhookHeadersDto,
  WebhookHeadersValidationResult,
} from '../dto/webhook-headers.dto';
import { AbsorbConfigService } from '../config/absorb-config.service';

/**
 * NestJS Guard for validating Absorb LMS webhook headers
 *
 * This guard implements comprehensive validation for incoming webhook requests from Absorb LMS,
 * ensuring security, authenticity, and proper formatting of webhook headers. It performs both
 * structural validation using class-validator decorators and business logic validation based
 * on configuration settings.
 *
 * ## Key Features:
 * - **Header Structure Validation**: Validates required and optional headers using DTOs
 * - **Security Validation**: Verifies HMAC signatures and timestamps for authenticity
 * - **Business Logic Validation**: Enforces configuration-based security policies
 * - **Error Handling**: Provides detailed error messages for debugging
 * - **Correlation Tracking**: Supports request correlation for distributed tracing
 *
 * ## Security Measures:
 * - HMAC-SHA256 signature verification (configurable)
 * - Timestamp validation to prevent replay attacks (configurable)
 * - Content-type validation for JSON payloads
 * - Case-insensitive header matching
 *
 * ## Usage:
 * Apply this guard to webhook endpoints to ensure only valid Absorb LMS requests are processed:
 * ```typescript
 * @UseGuards(WebhookHeadersValidationGuard)
 * @Post('webhooks')
 * async handleWebhook(@Req() request: Request) {
 *   // request.validatedHeaders contains the validated headers
 * }
 * ```
 *
 * @see {@link AbsorbWebhookHeadersDto} for header structure definition
 * @see {@link AbsorbConfigService} for configuration options
 * <AUTHOR> Bridge Team
 * @since 1.0.0
 */
@Injectable()
export class WebhookHeadersValidationGuard implements CanActivate {
  private readonly logger = new Logger(WebhookHeadersValidationGuard.name);

  /**
   * Creates an instance of WebhookHeadersValidationGuard
   *
   * @param configService - Service providing Absorb LMS configuration settings
   */
  constructor(private readonly configService: AbsorbConfigService) {}

  /**
   * Main guard method that validates webhook headers before allowing request processing
   *
   * This method is automatically called by NestJS when the guard is applied to a route.
   * It extracts headers from the incoming request, validates them against the expected
   * structure and business rules, and either allows the request to proceed or throws
   * an appropriate exception.
   *
   * ## Process Flow:
   * 1. Extract request and correlation ID for tracing
   * 2. Validate headers using DTO validation and business rules
   * 3. Attach validated headers to request object for downstream use
   * 4. Log validation results for monitoring and debugging
   *
   * ## Request Enhancement:
   * On successful validation, the request object is enhanced with:
   * - `validatedHeaders`: Validated and transformed header object
   * - `correlationId`: Request correlation ID for tracing
   *
   * @param context - NestJS execution context containing request information
   * @returns Promise<boolean> - true if validation passes, throws exception otherwise
   *
   * @throws {BadRequestException} When header validation fails with detailed error messages
   * @throws {UnauthorizedException} When an unexpected error occurs during validation
   *
   * @example
   * ```typescript
   * // Applied to a controller method
   * @UseGuards(WebhookHeadersValidationGuard)
   * @Post('webhook')
   * async handleWebhook(@Req() request: RequestWithValidatedHeaders) {
   *   const headers = request.validatedHeaders; // Validated headers available here
   * }
   * ```
   */
  async canActivate(context: ExecutionContext): Promise<boolean> {
    /**
     * Extended Request interface to include validation-specific properties
     * @interface RequestWithCorrelationIdAndValidatedHeaders
     */
    interface RequestWithCorrelationIdAndValidatedHeaders extends Request {
      /** Correlation ID for request tracing */
      correlationId?: string;
      /** Validated headers object after successful validation */
      validatedHeaders?: unknown;
    }

    const request = context
      .switchToHttp()
      .getRequest<RequestWithCorrelationIdAndValidatedHeaders>();
    const correlationId = request.correlationId ?? 'unknown';

    try {
      const validationResult = await this.validateHeaders(request.headers, correlationId);

      if (!validationResult.isValid) {
        this.logger.warn('Headers validation failed', {
          correlationId,
          errors: validationResult.errors,
        });
        throw new BadRequestException({
          message: 'Invalid webhook headers',
          errors: validationResult.errors,
          statusCode: 400,
        });
      }

      // Attach validated headers to request for downstream use
      request.validatedHeaders = validationResult.headers;
      this.logger.debug('Headers validation successful', { correlationId });
      return true;
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      this.logger.error('Header validation error', { correlationId });
      throw new UnauthorizedException('Header validation failed');
    }
  }

  /**
   * Validates webhook headers using comprehensive validation pipeline
   *
   * This method orchestrates the complete header validation process, combining
   * structural validation (using class-validator) with business logic validation
   * based on configuration settings. It provides detailed error reporting for
   * debugging and security monitoring.
   *
   * ## Validation Pipeline:
   * 1. **Header Transformation**: Maps raw headers to DTO property names
   * 2. **Structural Validation**: Validates using class-validator decorators
   * 3. **Business Logic Validation**: Applies configuration-based security rules
   * 4. **Error Aggregation**: Collects and formats all validation errors
   *
   * ## Validation Options:
   * - `whitelist: true` - Only allow properties defined in the DTO
   * - `forbidNonWhitelisted: false` - Don't throw on extra properties
   * - `skipMissingProperties: false` - Validate all properties including optional ones
   * - `validationError: { target: false, value: false }` - Exclude sensitive data from errors
   *
   * @param headers - Raw headers object from the HTTP request
   * @param correlationId - Request correlation ID for logging and tracing
   * @returns Promise<WebhookHeadersValidationResult> - Validation result with success status and errors
   *
   * @example
   * ```typescript
   * const result = await this.validateHeaders(request.headers, 'req-123');
   * if (result.isValid) {
   *   // Use result.headers (validated DTO)
   * } else {
   *   // Handle result.errors
   * }
   * ```
   */
  private async validateHeaders(
    headers: Record<string, unknown>,
    correlationId: string,
  ): Promise<WebhookHeadersValidationResult> {
    try {
      // Transform raw headers to match DTO property names
      const transformedHeaders = this.transformHeaders(headers);

      // Convert to DTO class instance for validation
      const headersDto = plainToClass(AbsorbWebhookHeadersDto, transformedHeaders);

      // Perform structural validation using class-validator
      const validationErrors = await validate(headersDto, {
        whitelist: true, // Only allow whitelisted properties
        forbidNonWhitelisted: false, // Don't reject extra properties
        skipMissingProperties: false, // Validate optional properties
        validationError: { target: false, value: false }, // Exclude sensitive data
      });

      if (validationErrors.length > 0) {
        const errors = this.formatValidationErrors(validationErrors);
        return { isValid: false, errors };
      }

      // Perform business logic validation
      const businessValidationErrors = this.performBusinessValidation(headersDto);
      if (businessValidationErrors.length > 0) {
        return { isValid: false, errors: businessValidationErrors };
      }

      return { isValid: true, headers: headersDto };
    } catch (error) {
      this.logger.error('Error during header validation', { correlationId, error });
      return { isValid: false, errors: ['Failed to validate headers due to internal error'] };
    }
  }

  /**
   * Transforms raw HTTP headers to match DTO property names
   *
   * This method maps incoming HTTP headers (which use kebab-case naming) to the
   * camelCase property names expected by the AbsorbWebhookHeadersDto. It handles
   * multiple possible header names for the same property and performs case-insensitive
   * matching to ensure compatibility with different HTTP clients and proxies.
   *
   * ## Header Mapping Strategy:
   * - Maps multiple possible header names to single DTO properties
   * - Handles vendor-specific variations (e.g., 'x-absorb-signature', 'absorb-signature')
   * - Supports proxy headers (x-forwarded-* headers)
   * - Includes distributed tracing headers (traceparent, tracestate)
   *
   * ## Supported Header Mappings:
   * - `absorbSignature`: ['absorb-signature', 'x-absorb-signature']
   * - `xAbsorbTimestamp`: ['x-absorb-timestamp', 'x-timestamp']
   * - Standard HTTP headers: user-agent, content-type, content-length, host
   * - Proxy headers: x-forwarded-for, x-forwarded-host, x-forwarded-proto
   * - Tracing headers: traceparent, tracestate
   *
   * @param headers - Raw headers object from HTTP request
   * @returns Record<string, unknown> - Transformed headers with DTO property names
   *
   * @example
   * ```typescript
   * const rawHeaders = { 'x-absorb-signature': 'abc123', 'content-type': 'application/json' };
   * const transformed = this.transformHeaders(rawHeaders);
   * // Result: { absorbSignature: 'abc123', contentType: 'application/json' }
   * ```
   */
  private transformHeaders(headers: Record<string, unknown>): Record<string, unknown> {
    const transformed: Record<string, unknown> = {};

    /**
     * Mapping of DTO property names to possible HTTP header names
     * Each property can have multiple possible header names for flexibility
     */
    const headerMappings = {
      absorbSignature: ['absorb-signature', 'x-absorb-signature'],
      userAgent: ['user-agent'],
      contentType: ['content-type'],
      contentLength: ['content-length'],
      host: ['host'],
      acceptEncoding: ['accept-encoding'],
      traceparent: ['traceparent'], // W3C Trace Context standard
      tracestate: ['tracestate'], // W3C Trace Context standard
      xForwardedFor: ['x-forwarded-for'],
      xForwardedHost: ['x-forwarded-host'],
      xForwardedProto: ['x-forwarded-proto'],
      xAbsorbTimestamp: ['x-absorb-timestamp', 'x-timestamp'],
    };

    // Transform each header using the mapping
    for (const [dtoProperty, possibleHeaders] of Object.entries(headerMappings)) {
      for (const headerName of possibleHeaders) {
        const value = this.findHeaderValue(headers, headerName);
        if (value !== undefined) {
          transformed[dtoProperty] = value;
          break; // Use first matching header name
        }
      }
    }

    return transformed;
  }

  /**
   * Finds header value using case-insensitive search with array handling
   *
   * HTTP headers can be case-insensitive and may appear as arrays when multiple
   * values are provided. This method handles both scenarios by:
   * 1. First attempting exact case match for performance
   * 2. Falling back to case-insensitive search if needed
   * 3. Extracting first value from arrays when present
   * 4. Ensuring type safety by validating string values
   *
   * ## Array Handling:
   * When headers contain multiple values (arrays), only the first string value
   * is returned. Non-string values are ignored for security and consistency.
   *
   * ## Case Sensitivity:
   * HTTP headers are case-insensitive per RFC 7230, so this method performs
   * case-insensitive matching when exact matches fail.
   *
   * @param headers - Headers object to search within
   * @param headerName - Name of the header to find (case-insensitive)
   * @returns string | undefined - First string value found, or undefined if not found
   *
   * @example
   * ```typescript
   * const headers = { 'Content-Type': 'application/json', 'X-Custom': ['value1', 'value2'] };
   *
   * this.findHeaderValue(headers, 'content-type');  // Returns: 'application/json'
   * this.findHeaderValue(headers, 'x-custom');      // Returns: 'value1'
   * this.findHeaderValue(headers, 'missing');       // Returns: undefined
   * ```
   */
  private findHeaderValue(
    headers: Record<string, unknown>,
    headerName: string,
  ): string | undefined {
    // Try exact case match first (performance optimization)
    if (headers[headerName] !== undefined) {
      const value = headers[headerName];
      if (Array.isArray(value)) {
        return typeof value[0] === 'string' ? value[0] : undefined;
      }
      return typeof value === 'string' ? value : undefined;
    }

    // Fall back to case-insensitive search
    const lowerHeaderName = headerName.toLowerCase();
    for (const [key, value] of Object.entries(headers)) {
      if (key.toLowerCase() === lowerHeaderName) {
        if (Array.isArray(value)) {
          return typeof value[0] === 'string' ? value[0] : undefined;
        }
        return typeof value === 'string' ? value : undefined;
      }
    }

    return undefined;
  }

  /**
   * Formats class-validator validation errors into user-friendly messages
   *
   * This method recursively processes validation errors from class-validator,
   * extracting constraint messages and handling nested validation errors.
   * It provides clean, readable error messages for API responses while
   * maintaining security by not exposing internal implementation details.
   *
   * ## Error Processing:
   * - Extracts constraint messages from validation errors
   * - Recursively processes nested validation errors (for nested objects)
   * - Flattens error hierarchy into a simple string array
   * - Filters out non-validation error objects for safety
   *
   * ## Security Considerations:
   * - Only processes known validation error structures
   * - Excludes target objects and values from error messages
   * - Provides sanitized error messages suitable for client consumption
   *
   * @param validationErrors - Array of validation errors from class-validator
   * @returns string[] - Array of formatted error messages
   *
   * @example
   * ```typescript
   * const errors = await validate(dto);
   * const messages = this.formatValidationErrors(errors);
   * // Result: ['absorb-signature header is required', 'content-type must be application/json']
   * ```
   */
  private formatValidationErrors(validationErrors: unknown[]): string[] {
    const errors: string[] = [];

    for (const error of validationErrors) {
      if (this.isValidationError(error)) {
        // Extract constraint messages
        if (error.constraints) {
          for (const constraint of Object.values(error.constraints)) {
            errors.push(constraint);
          }
        }

        // Recursively process nested validation errors
        if (error.children && error.children.length > 0) {
          const nestedErrors = this.formatValidationErrors(error.children);
          errors.push(...nestedErrors);
        }
      }
    }

    return errors;
  }

  /**
   * Type guard to safely identify class-validator validation error objects
   *
   * This type guard ensures that we only process objects that match the expected
   * structure of class-validator validation errors. It provides type safety and
   * prevents runtime errors when processing unknown error objects.
   *
   * ## Validation Error Structure:
   * A valid validation error must have either:
   * - `constraints`: Object containing constraint violation messages
   * - `children`: Array of nested validation errors
   * - Both properties (for complex validation scenarios)
   *
   * @param error - Unknown object to check
   * @returns boolean - true if object is a validation error, false otherwise
   *
   * @example
   * ```typescript
   * if (this.isValidationError(someObject)) {
   *   // TypeScript now knows someObject has constraints and/or children properties
   *   console.log(someObject.constraints);
   * }
   * ```
   */
  private isValidationError(error: unknown): error is {
    constraints?: Record<string, string>;
    children?: unknown[];
  } {
    return (
      typeof error === 'object' && error !== null && ('constraints' in error || 'children' in error)
    );
  }

  /**
   * Performs configuration-driven business logic validation on webhook headers
   *
   * This method applies security and business rules that go beyond basic structural
   * validation. It enforces configuration-based policies for signature verification,
   * timestamp validation, and content type requirements. These validations are
   * essential for webhook security and proper integration with Absorb LMS.
   *
   * ## Business Validation Rules:
   *
   * ### 1. Signature Verification
   * - **When**: `ABSORB_WEBHOOK_ENABLE_SIGNATURE_VERIFICATION=true`
   * - **Rule**: `absorb-signature` header must be present
   * - **Purpose**: Ensures webhook authenticity via HMAC-SHA256 verification
   *
   * ### 2. Timestamp Validation
   * - **When**: `ABSORB_WEBHOOK_ENABLE_TIMESTAMP_VALIDATION=true`
   * - **Rule**: Request timestamp must be within configured tolerance window
   * - **Purpose**: Prevents replay attacks by rejecting old requests
   * - **Default Tolerance**: 300 seconds (5 minutes)
   *
   * ### 3. Content Type Validation
   * - **Rule**: Content-Type must be `application/json` when present
   * - **Purpose**: Ensures proper JSON payload processing
   * - **Flexibility**: Optional header, only validated when present
   *
   * ## Configuration Dependencies:
   * - Uses `AbsorbConfigService` for feature flags and tolerance settings
   * - Respects environment-based security configuration
   * - Allows runtime adjustment of validation strictness
   *
   * @param headers - Validated headers DTO from structural validation
   * @returns string[] - Array of business validation error messages (empty if valid)
   *
   * @example
   * ```typescript
   * const errors = this.performBusinessValidation(validatedHeaders);
   * if (errors.length > 0) {
   *   // Handle business rule violations
   *   console.log('Business validation failed:', errors);
   * }
   * ```
   */
  private performBusinessValidation(headers: AbsorbWebhookHeadersDto): string[] {
    const errors: string[] = [];

    // 1. Signature Verification Requirement
    if (this.configService.isSignatureVerificationEnabled() && !headers.absorbSignature) {
      errors.push('absorb-signature header is required when signature verification is enabled');
    }

    // 2. Timestamp Validation (Replay Attack Prevention)
    if (this.configService.isTimestampValidationEnabled() && headers.xAbsorbTimestamp) {
      const timestamp = parseInt(headers.xAbsorbTimestamp, 10);
      const now = Math.floor(Date.now() / 1000);
      const tolerance = this.configService.getTimestampTolerance();
      const age = Math.abs(now - timestamp);

      if (age > tolerance) {
        errors.push(`Request timestamp is outside tolerance window: ${age}s > ${tolerance}s`);
      }
    }

    // 3. Content-Type Validation
    if (headers.contentType && !headers.contentType.includes('application/json')) {
      errors.push('content-type must be application/json for webhook requests');
    }

    return errors;
  }
}
