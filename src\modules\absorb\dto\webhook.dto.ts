import { IsD<PERSON><PERSON><PERSON>, IsEnum, IsNotEmpty, IsObject, IsOptional, IsString } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { WebhookEventType } from '../enums';

/**
 * AbsorbLMS webhook payload DTO based on official documentation
 * Represents the lean payload structure sent by AbsorbLMS webhooks
 */
export class AbsorbWebhookPayloadDto {
  @ApiProperty({
    description: 'The name of the webhook event',
    enum: WebhookEventType,
    example: WebhookEventType.USER_CREATED,
  })
  @IsEnum(WebhookEventType)
  @IsNotEmpty()
  eventName: WebhookEventType;

  @ApiProperty({
    description: 'ID of the user associated with the event',
    example: '550e8400-e29b-41d4-a716-446655440000',
    required: false,
  })
  @IsOptional()
  @IsString()
  userId?: string;

  @ApiProperty({
    description: 'ID of the admin who triggered the event',
    example: '550e8400-e29b-41d4-a716-446655440001',
    required: false,
  })
  @IsOptional()
  @IsString()
  adminId?: string;

  @ApiProperty({
    description: 'Client ID for the webhook event',
    example: 'client-123',
    required: false,
  })
  @IsOptional()
  @IsString()
  clientId?: string;

  @ApiProperty({
    description: 'Correlation ID for tracking the event',
    example: 'corr-456',
    required: false,
  })
  @IsOptional()
  @IsString()
  correlationId?: string;

  @ApiProperty({
    description: 'Additional transaction information',
    required: false,
  })
  @IsOptional()
  @IsObject()
  transactionInfo?: Record<string, any>;

  // Enriched data fields (populated by Integration API)
  @ApiProperty({
    description: 'Enriched user details',
    required: false,
  })
  @IsOptional()
  @IsObject()
  userDetails?: any;

  @ApiProperty({
    description: 'Enriched admin details',
    required: false,
  })
  @IsOptional()
  @IsObject()
  adminDetails?: any;

  @ApiProperty({
    description: 'Enriched course details',
    required: false,
  })
  @IsOptional()
  @IsObject()
  courseDetails?: any;

  @ApiProperty({
    description: 'Enriched enrollment details',
    required: false,
  })
  @IsOptional()
  @IsObject()
  enrollmentDetails?: any;

  @ApiProperty({
    description: 'Enriched certificate details',
    required: false,
  })
  @IsOptional()
  @IsObject()
  certificateDetails?: any;

  @ApiProperty({
    description: 'Enriched competency details',
    required: false,
  })
  @IsOptional()
  @IsObject()
  competencyDetails?: any;

  @ApiProperty({
    description: 'Enriched department details',
    required: false,
  })
  @IsOptional()
  @IsObject()
  departmentDetails?: any;

  @ApiProperty({
    description: 'Enriched group details',
    required: false,
  })
  @IsOptional()
  @IsObject()
  groupDetails?: any;

  @ApiProperty({
    description: 'Enriched learning path details',
    required: false,
  })
  @IsOptional()
  @IsObject()
  learningPathDetails?: any;

  // Event-specific ID fields
  @ApiProperty({
    description: 'Course ID for course-related events',
    example: '550e8400-e29b-41d4-a716-446655440002',
    required: false,
  })
  @IsOptional()
  @IsString()
  courseId?: string;

  @ApiProperty({
    description: 'Enrollment ID for enrollment-related events',
    example: '550e8400-e29b-41d4-a716-446655440003',
    required: false,
  })
  @IsOptional()
  @IsString()
  enrollmentId?: string;

  @ApiProperty({
    description: 'Certificate ID for certificate-related events',
    example: '550e8400-e29b-41d4-a716-446655440004',
    required: false,
  })
  @IsOptional()
  @IsString()
  certificateId?: string;

  @ApiProperty({
    description: 'Competency ID for competency-related events',
    example: '550e8400-e29b-41d4-a716-446655440005',
    required: false,
  })
  @IsOptional()
  @IsString()
  competencyId?: string;

  @ApiProperty({
    description: 'Department ID for department-related events',
    example: '550e8400-e29b-41d4-a716-446655440006',
    required: false,
  })
  @IsOptional()
  @IsString()
  departmentId?: string;

  @ApiProperty({
    description: 'Group ID for group-related events',
    example: '550e8400-e29b-41d4-a716-446655440007',
    required: false,
  })
  @IsOptional()
  @IsString()
  groupId?: string;

  @ApiProperty({
    description: 'Learning path ID for learning path-related events',
    example: '550e8400-e29b-41d4-a716-446655440008',
    required: false,
  })
  @IsOptional()
  @IsString()
  learningPathId?: string;
}

/**
 * AbsorbLMS webhook response DTO
 */
export class AbsorbWebhookResponseDto {
  @ApiProperty({
    description: 'Processing status',
    example: 'success',
    enum: ['success', 'error', 'pending'],
  })
  @IsNotEmpty()
  @IsString()
  status: 'success' | 'error' | 'pending';

  @ApiProperty({
    description: 'Response message',
    example: 'Webhook processed successfully',
  })
  @IsNotEmpty()
  @IsString()
  message: string;

  @ApiProperty({
    description: 'Event name that was processed',
    enum: WebhookEventType,
    required: false,
  })
  @IsOptional()
  @IsEnum(WebhookEventType)
  eventName?: WebhookEventType;

  @ApiProperty({
    description: 'Processing timestamp',
    example: '2023-01-01T00:00:00.000Z',
  })
  @IsNotEmpty()
  @IsDateString()
  processedAt: string;

  @ApiProperty({
    description: 'Correlation ID for tracking',
    required: false,
  })
  @IsOptional()
  @IsString()
  correlationId?: string;

  @ApiProperty({
    description: 'Additional response data',
    required: false,
  })
  @IsOptional()
  @IsObject()
  data?: Record<string, any>;
}
