import { WebhookEventType } from '@/modules/absorb/enums';
import { BaseWebhookPayload } from '../absorb-base-webhook-payload.interface';

/**
 * Enrollment completion payloads
 */
export interface EnrollmentCompletedByAdminPayload extends BaseWebhookPayload {
  readonly eventType: WebhookEventType.ENROLLMENT_COMPLETED_BY_ADMIN;
  readonly completionDate: string; // ISO 8601 datetime
  readonly finalScore?: number;
}
