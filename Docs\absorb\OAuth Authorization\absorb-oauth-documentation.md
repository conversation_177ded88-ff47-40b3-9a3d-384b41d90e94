# Absorb Integration API - OAuth 2.0 Authentication

## Overview

Many of the resources in a client portal are protected and require that the user is authenticated. The Absorb Integration API uses OpenID Connect (authorization code flow) to provide an access token and refresh token.

This token must then be sent in the `Authorization` header when making requests to protected resources.

```
Authorization: Bearer <token>
```

### Token Validity

- **Access token**: Valid for 4 hours after it is issued. After the time has elapsed, another login request is required to get a new token.
- **Refresh token**: Lasts for 1 year.

### Security Requirements

As the token represents an authenticated user, it must be protected. Bearer authentication should only be used over HTTPS; Absorb LMS does not support HTTP.

## Server Endpoints

- **US**: `https://rest.myabsorb.com`
- **CA**: `https://rest.myabsorb.ca`
- **EU**: `https://rest.myabsorb.eu`
- **AU**: `https://rest.myabsorb.com.au`

## OAuth Authorization Code Flow

### 1. Request Authorization Code

To request an authorization code, user makes an authentication request to authorization endpoint. Please note that the authorization code is 1-time use.

**Endpoint**: `GET /oauth/authorize`

**Note**: Header is not required for requesting an authorization code. This call is not made via postman but entered into a browser.

#### Query Parameters

| Name            | Type   | Description                                                                                               | Required |
| --------------- | ------ | --------------------------------------------------------------------------------------------------------- | -------- |
| `client_id`     | string | The unique API client identifier. Location: Portal Settings -> Integration API OAuth Client ID            | Yes      |
| `client_secret` | string | The unique API client secret identifier. Location: Portal Settings -> Integration API OAuth Client Secret | Yes      |
| `redirect_uri`  | string | The URI the users are sent back to after authorization. Should always be https.                           | Yes      |
| `response_type` | string | The value of this field should always be "code".                                                          | Yes      |
| `scope`         | string | Space-delimited list of member permissions your application is requesting. Supported scope: "admin.v1"    | Yes      |
| `state`         | string | A unique string value of your choice that is hard to guess.                                               | Yes      |

#### Example Request

```
https://clientRoute.com/oauth/authorize?client_id=_ahKweh2tdKdNMKnecwo&client_secret=iBHVbc0fn7vdCgvMxDAy7fWNlpQCwAcQoeCNYEDboahcUime01&redirect_uri=https://example.com&response_type=code&scope=admin.v1&state=anyString
```

#### Flow

1. It will redirect to the login page for the user to login
2. If the login is successful, the consent page will pop up
3. After the user provides the consent, the server will redirect to the URL provided in the `redirect_uri` query parameter and provide the authorization code

### 2. Get Refresh Token

Exchange an authorization code for a refresh token. Make a request to token endpoint, providing the authorization code received in the previous step.

**Endpoint**: `POST /oauth/token`

#### Headers

| Name            | Description                                    |
| --------------- | ---------------------------------------------- |
| `x-api-version` | The value of this header should always be "v1" |

#### Request Body (x-www-form-urlencoded)

| Name            | Type   | Description                                                                                      |
| --------------- | ------ | ------------------------------------------------------------------------------------------------ |
| `grant_type`    | string | The value of this field should always be "authorization_code"                                    |
| `client_id`     | string | Portal Settings -> Integration API OAuth Client ID                                               |
| `client_secret` | string | Portal Settings -> Integration API OAuth Client Secret                                           |
| `code`          | string | The authorization code received in previous step                                                 |
| `nonce`         | string | String value used to associate a client session with an ID Token, and to mitigate replay attacks |
| `redirect_uri`  | string | The URI the users are sent back to after authorization. Should always be https                   |

#### Example Response (201 Created)

```json
{
  "access_token": "**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
  "token_type": "bearer",
  "expires_in": 14399,
  "refresh_token": "09RR0D5mp5J56i4GTPEjdhMyMhw6t5IhTad5ErDD"
}
```

#### Response Fields

| Name            | Type    | Description                                                 |
| --------------- | ------- | ----------------------------------------------------------- |
| `access_token`  | string  | The access token for the application                        |
| `token_type`    | string  | The type of this access token (always "bearer")             |
| `expires_in`    | integer | The number of seconds remaining until the token expires     |
| `refresh_token` | string  | The refresh token, can be used to acquire new access tokens |

### 3. Get Access Token (Using Refresh Token)

Recreate an access token using a refresh token to login on behalf of a given user. Access tokens only last 4 hours. Once access token is expired, you can use the refresh token to acquire a new access token. You can also use refresh token to acquire an access token on behalf of another learner.

**Endpoint**: `POST /oauth/token`

#### Headers

| Name            | Description                                    |
| --------------- | ---------------------------------------------- |
| `x-api-version` | The value of this header should always be "v1" |

#### Request Body (x-www-form-urlencoded)

| Name            | Type   | Description                                                                                            | Required |
| --------------- | ------ | ------------------------------------------------------------------------------------------------------ | -------- |
| `grant_type`    | string | The value of this field should always be "refresh_token"                                               | Yes      |
| `client_id`     | string | Portal Settings -> Integration API OAuth Client ID                                                     | Yes      |
| `client_secret` | string | Portal Settings -> Integration API OAuth Client Secret                                                 | Yes      |
| `refresh_token` | string | The refresh token issued by the server in previous step                                                | Yes      |
| `nonce`         | string | String value used to associate a client session with an ID Token, and to mitigate replay attacks       | Yes      |
| `on-behalf-of`  | string | (Optional) The username of another user you would like to be logged in as                              | No       |
| `scope`         | string | Space-delimited list of member permissions your application is requesting. Supported scope: "admin.v1" | No       |

**Note**: The `on-behalf-of` parameter is only used when you want to acquire an access token on behalf of another learner.

### 4. Make Request Using Access Token

Once you've obtained an access token, you can start making authenticated API requests by including an Authorization header in the HTTP call.

#### Example Request

```
GET my-profile
Authorization: Bearer {access_token}
```

If you are using the access token on behalf of another user, you will see the info of the user you are on behalf of in the response.

## API Specification Details

### Data Models

#### OauthTokenRefreshTokenRequest

Request to retrieve Refresh Token

```yaml
type: object
properties:
  grant_type:
    type: string
    description: The value of this field should always be "authorization_code" or "refresh_token"
  client_id:
    type: string
    description: Portal Settings -> Integration API OAuth Client ID
  client_secret:
    type: string
    description: Portal Settings -> Integration API OAuth Client Secret
  nonce:
    type: string
    description: String value used to associate a client session with an ID Token, and to mitigate replay attacks
  code:
    type: string
    description: The authorization code received in oauth/authorize step (only used in get refresh token call)
  on-behalf-of:
    type: string
    description: Username you want to sign in as (only used in on-behalf-of call)
  refresh_token:
    type: string
    description: Refresh token from get refresh token oauth/token call (only used in on-behalf-of call)
  scope:
    type: string
    description: Space-delimited list of member permissions your application is requesting (only used in on-behalf-of call)
```

#### AuthenticationResponseResource

Resource that represents user authentication.

```yaml
type: object
properties:
  access_token:
    type: string
    description: Access token that represents an authenticated user. Required for protected API endpoints
  token_type:
    type: string
    description: This value will always be "bearer"
  expires_in:
    type: integer
    description: The number of seconds remaining until the token expires
  refresh_token:
    type: string
    description: The refresh token, can be used to acquire new access tokens
```

#### AuthenticationErrorResponseResource

Resource that represents an error in user authentication.

```yaml
type: object
properties:
  error:
    type: string
    description: The specific error code indicating the issue
```

## Error Responses

### 400 Bad Request

Invalid parameter(s) passed, see error code for further information.

Response includes an `AuthenticationErrorResponseResource` with specific error codes.

## Security Definitions

### x-api-key

- Type: apiKey
- Name: x-api-key
- In: header

## Important Notes

1. **Authorization Code**: The authorization code is single-use only. Once used to obtain a refresh token, it cannot be reused.

2. **Token Security**: Always protect your tokens. They represent authenticated users and should never be exposed publicly.

3. **HTTPS Only**: Absorb LMS does not support HTTP connections. All API calls must be made over HTTPS.

4. **Token Refresh**: When your access token expires (after 4 hours), use your refresh token to obtain a new access token without requiring user re-authentication.

5. **On-Behalf-Of**: The "on-behalf-of" feature allows administrators to perform actions on behalf of other users, useful for administrative tasks and user support.

6. **Supported Scopes**: Currently, the supported scope is "admin.v1" for administrative permissions.

## Complete Flow Summary

1. **Initial Authorization**: Direct user to `/oauth/authorize` endpoint with required parameters
2. **User Authentication**: User logs in and provides consent
3. **Get Refresh Token**: Exchange authorization code for refresh token at `/oauth/token`
4. **Get Access Token**: Use refresh token to obtain access token (same endpoint)
5. **API Calls**: Include access token in Authorization header for all API requests
6. **Token Refresh**: When access token expires, use refresh token to get new access token
