x-generator: NSwag v13.18.2.0 (NJsonSchema v10.8.0.0 (Newtonsoft.Json v11.0.0.0))
swagger: '2.0'
info:
  title: Absorb Integration API
  version: v2
consumes:
  - application/json
produces:
  - application/json
paths:
  /users/{userId}/user-management-settings:
    get:
      tags:
        - User Management Settings
      summary: Get a user's management settings.
      operationId: UserManagementSettings_GetUserManagementSettings
      parameters:
        - type: string
          name: userId
          in: path
          required: true
          description: The user ID.
          format: guid
          x-nullable: false
        - type: string
          name: x-api-key
          in: header
        - type: string
          name: x-api-version
          in: header
          description: '2'
      responses:
        '200':
          x-nullable: false
          description: ''
          schema:
            $ref: '#/definitions/UserManagementSettingsResource'
        '404':
          x-nullable: false
          description: The specified user does not exist.
          schema:
            $ref: '#/definitions/GenericErrorResponse'
      security:
        - api_key: []
    put:
      tags:
        - User Management Settings
      summary: Update user management settings.
      operationId: UserManagementSettings_UpdateUserManagementSettings
      parameters:
        - type: string
          name: userId
          in: path
          required: true
          description: The user ID.
          format: guid
          x-nullable: false
        - name: requestBody
          in: body
          description: The request to update the user's management settings.
          schema:
            $ref: '#/definitions/UpdateUserManagementSettingsRequestBody'
          x-nullable: true
        - type: string
          name: x-api-key
          in: header
        - type: string
          name: x-api-version
          in: header
          description: '2'
      responses:
        '200':
          description: ''
        '400':
          x-nullable: false
          description: Invalid user management settings.
          schema:
            $ref: '#/definitions/GenericErrorResponse'
        '403':
          x-nullable: false
          description: User does not have permission to update user management settings.
          schema:
            $ref: '#/definitions/GenericErrorResponse'
        '404':
          x-nullable: false
          description: User not found.
          schema:
            $ref: '#/definitions/GenericErrorResponse'
        '422':
          x-nullable: false
          description: A validation error occured with the provided parameters.
          schema:
            $ref: '#/definitions/GenericErrorResponse'
      security:
        - api_key: []
  /users:
    get:
      tags:
        - Users
      summary: List users.
      description: >-
        Lists all selected LMS users that are available to the current,
        authenticated administrator.
      operationId: Users_GetUsers
      parameters:
        - type: string
          name: email
          in: query
          description: '*(Deprecated)* The specified email to filter by.'
          x-nullable: true
        - type: string
          name: username
          in: query
          description: '*(Deprecated)* The specified username to filter by.'
          x-nullable: true
        - type: string
          name: modifiedSince
          in: query
          description: >-
            *(Deprecated)* The specified date for filtering users that were
            edited after this date.
          format: date-time
          x-nullable: true
        - type: string
          name: externalId
          in: query
          description: '*(Deprecated)* The specified external ID to filter by.'
          x-nullable: true
        - type: string
          name: limit
          in: query
          description: >-
            *(Deprecated)* The maximum number of items to return in the current
            page of the collection.
          x-nullable: true
        - type: string
          name: offset
          in: query
          description: '*(Deprecated)* The number of pages to offset into the collection.'
          x-nullable: true
        - type: string
          name: _filter
          in: query
          description: >-
            One or more filter operations to be performed on the collection.

            The referenced fields must allow filtering. See the respective
            report's schema for which fields can be filtered. 
                        
            Supports most of the [OData filter
            syntax](http://docs.oasis-open.org/odata/odata/v4.01/cs01/part2-url-conventions/odata-v4.01-cs01-part2-url-conventions.html#sec_SystemQueryOptionfilter).
                        
            Supported operations:
                * eq,
                * ne,
                * gt,
                * ge,
                * lt,
                * le,
                * and,
                * or,
                * not,
                * ()

            Supported functions:
                * substringof('value',fieldName),
                * endswith(fieldName,'value'),
                * startswith(fieldName,'value'),
                * tolower(fieldName),
                * toupper(fieldName)
                        
            Examples:

            * _filter=`firstname eq 'Jeffrey'`

            * _filter=`id eq guid'a14c149a-2ce0-41d4-b532-02189ad3cb22'`

            * _filter=`startsWith(lastname,'leb') or dateAdded ge
            datetime'1998-03-06T20:38:07Z'`
          x-nullable: true
        - type: string
          name: _sort
          in: query
          description: >-
            Optional list of comma-separated fields to sort the collection by.


            The referenced fields must allow sorting. See the respective
            report's schema for which fields can be sorted. 

            If not specified, all report fields are returned.


            To sort in a descending manner, prefix the field name with `-`. For
            example, 

            `_sort=name,-date`, sorts the collection of items by `name`
            (ascending) 

            and then by `date` (descending).
          x-nullable: true
        - type: string
          name: _limit
          in: query
          description: >-
            The maximum number of items to return in the current page of the
            collection.
          x-nullable: true
        - type: string
          name: _offset
          in: query
          description: The number of pages to offset into the collection.
          x-nullable: true
        - type: string
          name: x-api-key
          in: header
        - type: string
          name: x-api-version
          in: header
          description: '2'
      responses:
        '200':
          x-nullable: false
          description: ''
          schema:
            $ref: '#/definitions/UsersResource'
        '422':
          x-nullable: false
          description: A validation error occured with the provided parameters.
          schema:
            $ref: '#/definitions/GenericErrorResponse'
      security:
        - api_key: []
    post:
      tags:
        - Users
      summary: Upsert user if the username or external ID matches.
      operationId: Users_UpsertUser
      parameters:
        - type: integer
          name: key
          in: query
          description: |-
            The identifier used to locate the user.

            Possible Enum Values: `0 = Username` `1 = ExternalId`
          x-schema:
            $ref: '#/definitions/UploadUsersKey'
          x-nullable: false
          enum:
            - 0
            - 1
        - name: requestBody
          in: body
          description: The request body specifying the properties to update.
          schema:
            $ref: '#/definitions/GenericUserRequestBody'
          x-nullable: true
        - type: string
          name: x-api-key
          in: header
        - type: string
          name: x-api-version
          in: header
          description: '2'
      responses:
        '200':
          x-nullable: false
          description: ''
          schema:
            $ref: '#/definitions/UpsertUserResource'
        '201':
          x-nullable: false
          description: ''
          schema:
            $ref: '#/definitions/UpsertUserResource'
        '400':
          x-nullable: false
          description: The required body paramaters were not included.
          schema:
            $ref: '#/definitions/GenericErrorResponse'
        '404':
          x-nullable: false
          description: User not found.
          schema:
            $ref: '#/definitions/GenericErrorResponse'
        '422':
          x-nullable: false
          description: A validation error occured with the provided parameters.
          schema:
            $ref: '#/definitions/GenericErrorResponse'
      security:
        - api_key: []
  /users/{userId}:
    get:
      tags:
        - Users
      summary: Get User.
      operationId: Users_GetUserById
      parameters:
        - type: string
          name: userId
          in: path
          required: true
          description: The user ID.
          format: guid
          x-nullable: false
        - type: string
          name: x-api-key
          in: header
        - type: string
          name: x-api-version
          in: header
          description: '2'
      responses:
        '200':
          x-nullable: false
          description: ''
          schema:
            $ref: '#/definitions/NonPagedUserResource'
        '404':
          x-nullable: false
          description: User not found.
          schema:
            $ref: '#/definitions/GenericErrorResponse'
      security:
        - api_key: []
    put:
      tags:
        - Users
      summary: Update user.
      operationId: Users_UpdateUser
      parameters:
        - type: string
          name: userId
          in: path
          required: true
          description: The user ID.
          format: guid
          x-nullable: false
        - name: requestBody
          in: body
          description: The request body to update a user.
          schema:
            $ref: '#/definitions/GenericUserRequestBody'
          x-nullable: true
        - type: string
          name: x-api-key
          in: header
        - type: string
          name: x-api-version
          in: header
          description: '2'
      responses:
        '201':
          x-nullable: false
          description: ''
          schema:
            $ref: '#/definitions/UserResource'
        '404':
          x-nullable: false
          description: User not found.
          schema:
            $ref: '#/definitions/GenericErrorResponse'
        '422':
          x-nullable: false
          description: A validation error occured with the provided parameters.
          schema:
            $ref: '#/definitions/GenericErrorResponse'
      security:
        - api_key: []
  /create-absorb-account:
    post:
      tags:
        - Users
      summary: Upsert user if the username or external ID matches.
      operationId: Users_UpsertUser2
      parameters:
        - type: integer
          name: key
          in: query
          description: |-
            The identifier used to locate the user.

            Possible Enum Values: `0 = Username` `1 = ExternalId`
          x-schema:
            $ref: '#/definitions/UploadUsersKey'
          x-nullable: false
          enum:
            - 0
            - 1
        - name: requestBody
          in: body
          description: The request body specifying the properties to update.
          schema:
            $ref: '#/definitions/GenericUserRequestBody'
          x-nullable: true
        - type: string
          name: x-api-key
          in: header
        - type: string
          name: x-api-version
          in: header
          description: '2'
      responses:
        '200':
          x-nullable: false
          description: ''
          schema:
            $ref: '#/definitions/UpsertUserResource'
        '201':
          x-nullable: false
          description: ''
          schema:
            $ref: '#/definitions/UpsertUserResource'
        '400':
          x-nullable: false
          description: The required body paramaters were not included.
          schema:
            $ref: '#/definitions/GenericErrorResponse'
        '404':
          x-nullable: false
          description: User not found.
          schema:
            $ref: '#/definitions/GenericErrorResponse'
        '422':
          x-nullable: false
          description: A validation error occured with the provided parameters.
          schema:
            $ref: '#/definitions/GenericErrorResponse'
      security:
        - api_key: []
  /users/upload:
    post:
      tags:
        - Users
      summary: Creates and/or updates Users from a List of Users.
      description: A maximum of 200 users can be uploaded per request.
      operationId: Users_UploadUsers
      parameters:
        - type: integer
          name: key
          in: query
          description: |-
            The identifier used to locate the user.

            Possible Enum Values: `0 = Username` `1 = ExternalId`
          x-schema:
            $ref: '#/definitions/UploadUsersKey'
          x-nullable: false
          enum:
            - 0
            - 1
        - name: users
          in: body
          description: The request body which lists users.
          schema:
            type: array
            items:
              $ref: '#/definitions/GenericUserRequestBody'
          x-nullable: true
        - type: string
          name: x-api-key
          in: header
        - type: string
          name: x-api-version
          in: header
          description: '2'
      responses:
        '200':
          x-nullable: false
          description: ''
          schema:
            type: array
            items:
              $ref: '#/definitions/UploadUsersResource'
        '201':
          x-nullable: false
          description: ''
          schema:
            type: array
            items:
              $ref: '#/definitions/UploadUsersResource'
        '400':
          x-nullable: false
          description: User(s) passed contain errors.
          schema:
            $ref: '#/definitions/GenericErrorResponse'
        '422':
          x-nullable: false
          description: A validation error occured with the provided parameters.
          schema:
            $ref: '#/definitions/GenericErrorResponse'
        '500':
          x-nullable: false
          description: Exceptional error occured while processing the request.
          schema:
            $ref: '#/definitions/GenericErrorResponse'
      security:
        - api_key: []
  /my-details:
    get:
      tags:
        - Users
      summary: Get currently authenticated User's details.
      operationId: Users_GetMyDetails
      parameters:
        - type: string
          name: x-api-key
          in: header
        - type: string
          name: x-api-version
          in: header
          description: '2'
      responses:
        '200':
          x-nullable: false
          description: ''
          schema:
            $ref: '#/definitions/NonPagedUserResource'
        '404':
          x-nullable: false
          description: User not found.
          schema:
            $ref: '#/definitions/GenericErrorResponse'
      security:
        - api_key: []
  /users/{userId}/competencies:
    get:
      tags:
        - Competencies
      summary: Lists the competencies for a user.
      operationId: Users_GetCompetenciesForUser
      parameters:
        - type: string
          name: userId
          in: path
          required: true
          description: The user ID.
          format: guid
          x-nullable: false
        - type: string
          name: _filter
          in: query
          description: >-
            One or more filter operations to be performed on the collection.

            The referenced fields must allow filtering. See the respective
            report's schema for which fields can be filtered. 
                        
            Supports most of the [OData filter
            syntax](http://docs.oasis-open.org/odata/odata/v4.01/cs01/part2-url-conventions/odata-v4.01-cs01-part2-url-conventions.html#sec_SystemQueryOptionfilter).
                        
            Supported operations:
                * eq,
                * ne,
                * gt,
                * ge,
                * lt,
                * le,
                * and,
                * or,
                * not,
                * ()

            Supported functions:
                * substringof('value',fieldName),
                * endswith(fieldName,'value'),
                * startswith(fieldName,'value'),
                * tolower(fieldName),
                * toupper(fieldName)
                        
            Examples:

            * _filter=`firstname eq 'Jeffrey'`

            * _filter=`id eq guid'a14c149a-2ce0-41d4-b532-02189ad3cb22'`

            * _filter=`startsWith(lastname,'leb') or dateAdded ge
            datetime'1998-03-06T20:38:07Z'`
          x-nullable: true
        - type: string
          name: _sort
          in: query
          description: >-
            Optional list of comma-separated fields to sort the collection by.


            The referenced fields must allow sorting. See the respective
            report's schema for which fields can be sorted. 

            If not specified, all report fields are returned.


            To sort in a descending manner, prefix the field name with `-`. For
            example, 

            `_sort=name,-date`, sorts the collection of items by `name`
            (ascending) 

            and then by `date` (descending).
          x-nullable: true
        - type: string
          name: _limit
          in: query
          description: >-
            The maximum number of items to return in the current page of the
            collection.
          x-nullable: true
        - type: string
          name: _offset
          in: query
          description: The number of pages to offset into the collection.
          x-nullable: true
        - type: string
          name: x-api-key
          in: header
        - type: string
          name: x-api-version
          in: header
          description: '2'
      responses:
        '200':
          x-nullable: false
          description: ''
          schema:
            $ref: '#/definitions/CompetenciesResource'
        '404':
          x-nullable: false
          description: User not found.
          schema:
            $ref: '#/definitions/GenericErrorResponse'
        '422':
          x-nullable: false
          description: A validation error occured with the provided parameters.
          schema:
            $ref: '#/definitions/GenericErrorResponse'
      security:
        - api_key: []
definitions:
  UserManagementSettingsResource:
    type: object
    description: Resource representing a user's management settings.
    properties:
      userTypes:
        type: array
        description: The user's types.
        items:
          $ref: '#/definitions/UserType'
      userManagementType:
        description: >-
          The types of users that can be managed.


          Possible Enum Values: `0 = None` `1 = Group` `2 = Department` `3 =
          All`
        allOf:
          - $ref: '#/definitions/UserManagementType'
      managedGroupId:
        type: string
        description: >-
          The ID of the group the user is managing.


          Only applicable, when UserTypes includes Admin and UserManagementType
          is Group.
        format: guid
      managedDepartments:
        type: array
        description: >-
          The list of departments the user is managing.
                      
          Only applicable, when UserTypes includes Admin and UserManagementType
          is Department.
        items:
          $ref: '#/definitions/DepartmentSelectionDefinition'
  Link:
    type: object
    properties:
      Rel:
        $ref: '#/definitions/Relation'
      Items:
        type: array
        items:
          $ref: '#/definitions/LinkItem'
  Relation:
    type: object
    properties:
      Name:
        type: string
  LinkItem:
    type: object
    properties:
      Href:
        type: string
      Name:
        type: string
      Templated:
        type: boolean
      Type:
        type: string
      Deprecation:
        type: string
      Profile:
        type: string
      Title:
        type: string
      Hreflang:
        type: string
  IEmbeddedResource:
    type: object
    x-abstract: true
    properties:
      Name:
        type: string
      Resources:
        type: array
        items:
          $ref: '#/definitions/IResource'
  IResource:
    type: object
    x-abstract: true
    properties:
      Links:
        type: array
        items:
          $ref: '#/definitions/Link'
      EmbeddedResources:
        type: array
        items:
          $ref: '#/definitions/IEmbeddedResource'
  UserType:
    type: string
    description: An enumeration of user types.
    x-enumNames:
      - Learner
      - Instructor
      - Admin
      - Manager
    enum:
      - Learner
      - Instructor
      - Admin
      - Manager
  UserManagementType:
    type: integer
    description: The allowable user management types.
    x-enumNames:
      - None
      - Group
      - Department
      - All
    enum:
      - 0
      - 1
      - 2
      - 3
  DepartmentSelectionDefinition:
    type: object
    description: A department selection which may include all subdepartments.
    required:
      - departmentId
      - includeSubDepartments
    properties:
      departmentId:
        type: string
        description: The department ID.
        format: guid
      includeSubDepartments:
        type: boolean
        description: >-
          If true all subdepartments should be included.

          If false only the specified department with no subdepartments should
          be included.
  GenericErrorResponse:
    type: object
    description: |-
      This class is NOT to be used as an actual response. This is to be used as
      the return type for non-200 responses in `SwaggerResponse` attributes.
    properties:
      validations:
        type: array
        description: The validations (if any) which caused the error.
        items:
          type: string
      code:
        description: The status code returned.
        allOf:
          - $ref: '#/definitions/HttpStatusCode'
      message:
        type: string
        description: The error message.
      term:
        type: string
        description: The general error term.
      _meta:
        type: object
        description: 'A list of meta data (if any). '
        additionalProperties:
          type: string
  HttpStatusCode:
    type: integer
    description: ''
    x-enumNames:
      - Continue
      - SwitchingProtocols
      - OK
      - Created
      - Accepted
      - NonAuthoritativeInformation
      - NoContent
      - ResetContent
      - PartialContent
      - MultipleChoices
      - Ambiguous
      - MovedPermanently
      - Moved
      - Found
      - Redirect
      - SeeOther
      - RedirectMethod
      - NotModified
      - UseProxy
      - Unused
      - TemporaryRedirect
      - RedirectKeepVerb
      - BadRequest
      - Unauthorized
      - PaymentRequired
      - Forbidden
      - NotFound
      - MethodNotAllowed
      - NotAcceptable
      - ProxyAuthenticationRequired
      - RequestTimeout
      - Conflict
      - Gone
      - LengthRequired
      - PreconditionFailed
      - RequestEntityTooLarge
      - RequestUriTooLong
      - UnsupportedMediaType
      - RequestedRangeNotSatisfiable
      - ExpectationFailed
      - UpgradeRequired
      - InternalServerError
      - NotImplemented
      - BadGateway
      - ServiceUnavailable
      - GatewayTimeout
      - HttpVersionNotSupported
  UpdateUserManagementSettingsRequestBody:
    type: object
    description: The request body for updating user management settings.
    properties:
      userTypes:
        type: array
        description: The user's types.
        items:
          $ref: '#/definitions/UserType'
      userManagementType:
        description: >-
          The type of admin the user should be updated to.
                      
          Only applicable, but required, when UserTypes includes Admin.


          Possible Enum Values: `0 = None` `1 = Group` `2 = Department` `3 =
          All`
        allOf:
          - $ref: '#/definitions/UserManagementType'
      managedGroupId:
        type: string
        description: >-
          The ID of the group the user should manage.


          Only applicable, but required, when UserTypes includes Admin and
          UserManagementType is Group.
        format: guid
      managedDepartments:
        type: array
        description: >-
          The list of departments the user should manage.
                      
          Only applicable, but required, when UserTypes includes Admin and
          UserManagementType is Department.
        items:
          $ref: '#/definitions/DepartmentSelectionDefinition'
  UsersResource:
    type: object
    properties:
      totalItems:
        type: integer
        description: The total number of items returned
        format: int32
      limit:
        type: integer
        description: The page size.
        format: int32
      offset:
        type: integer
        description: The page number.
        format: int32
      returnedItems:
        type: integer
        description: The number of items returned.
        format: int32
      users:
        type: array
        description: The paged collection of resources.
        items:
          $ref: '#/definitions/UserResource'
  UserResource:
    type: object
    properties:
      id:
        type: string
        description: |-
          `Filterable` 

          The unique user identifier.
        format: guid
      departmentId:
        type: string
        description: |-
          `Sortable` `Filterable` 

          The user's department ID.
        format: guid
      firstName:
        type: string
        description: |-
          `Sortable` `Filterable` 

          The first name.
      middleName:
        type: string
        description: |-
          `Sortable` `Filterable` 

          The middle name.
      lastName:
        type: string
        description: |-
          `Sortable` `Filterable` 

          The last name.
      username:
        type: string
        description: |-
          `Default Sort` `Sortable` `Filterable` 

          The unique username.
      password:
        type: string
        description: Password Required for User Creation.
        maxLength: 255
        minLength: 0
      emailAddress:
        type: string
        description: |-
          `Sortable` `Filterable` 

          The user contact email address.
      externalId:
        type: string
        description: |-
          `Sortable` `Filterable` 

          The external ID used for integrations with external systems.
      ccEmailAddresses:
        type: array
        description: |-
          `Filterable` 

          List of email addresses, maximum 5 (MaxCcEmailSize)
        items:
          type: string
      languageId:
        type: integer
        description: |-
          `Sortable` `Filterable` 

          The language id.
        format: int32
      gender:
        description: |-
          `Sortable` `Filterable` 

          The gender.

          Possible Enum Values: `0 = None` `1 = Male` `2 = Female`
        allOf:
          - $ref: '#/definitions/Gender'
      address:
        type: string
        description: |-
          `Sortable` `Filterable` 

          The mailing address.
      address2:
        type: string
        description: |-
          `Sortable` `Filterable` 

          The mailing address, part two.
      city:
        type: string
        description: |-
          `Sortable` `Filterable` 

          The city.
      provinceId:
        type: string
        description: |-
          `Sortable` `Filterable` 

          The province id.
        format: guid
      countryId:
        type: string
        description: |-
          `Sortable` `Filterable` 

          The country id.
        format: guid
      postalCode:
        type: string
        description: |-
          `Sortable` `Filterable` 

          The postal/zip code.
      phone:
        type: string
        description: |-
          `Sortable` `Filterable` 

          The phone number.
      employeeNumber:
        type: string
        description: |-
          `Sortable` `Filterable` 

          The employee number.
      location:
        type: string
        description: |-
          `Sortable` `Filterable` 

          The location.
      jobTitle:
        type: string
        description: |-
          `Sortable` `Filterable` 

          The job title.
      referenceNumber:
        type: string
        description: |-
          `Sortable` `Filterable` 

          The reference number.
      dateHired:
        type: string
        description: |-
          `Sortable` `Filterable` 

          If applicable, the date the user was hired.
        format: date-time
      dateTerminated:
        type: string
        description: |-
          `Sortable` `Filterable` 

          If applicable, the date the user's employment was terminated.
        format: date-time
      dateEdited:
        type: string
        description: |-
          `Sortable` `Filterable` 

          The date the user is edited.
        format: date-time
      dateAdded:
        type: string
        description: |-
          `Sortable` `Filterable` 

          The date the user was added.
        format: date-time
      lastLoginDate:
        type: string
        description: |-
          `Sortable` `Filterable` 

          The last date the user successfully logged in.
        format: date-time
      notes:
        type: string
        description: |-
          `Filterable` 

          The notes.
      customFields:
        description: |-
          `Sortable` `Filterable` 

          The custom fields.
        allOf:
          - $ref: '#/definitions/CustomFieldsResource'
      roleIds:
        type: array
        description: |-
          `Filterable` 

          The role ids.
        items:
          type: string
          format: guid
      activeStatus:
        description: |-
          `Sortable` `Filterable` 

          The user's active status as UserActiveStatus .

          Possible Enum Values: `0 = Active` `1 = Inactive`
        allOf:
          - $ref: '#/definitions/UserActiveStatus'
      isLearner:
        type: boolean
        description: |-
          `Sortable` `Filterable` 

          True if the user is a learner, otherwise false.
      isAdmin:
        type: boolean
        description: |-
          `Sortable` `Filterable` 

          True if the user is an administrator, otherwise false.
      isInstructor:
        type: boolean
        description: |-
          `Sortable` `Filterable` 

          True if the user is an instructor, otherwise false.
      isManager:
        type: boolean
        description: |-
          `Sortable` `Filterable` 

          True if the user is a manager, otherwise false.
      supervisorId:
        type: string
        description: |-
          `Sortable` `Filterable` 

          The supervisor id.
        format: guid
      hasUsername:
        type: boolean
        description: Determines whether the user has a username or not.
  Gender:
    type: integer
    description: 'Defines an enumeration for genders. '
    x-enumNames:
      - None
      - Male
      - Female
    enum:
      - 0
      - 1
      - 2
  CustomFieldsResource:
    type: object
    properties:
      decimal1:
        type: number
        format: decimal
      decimal2:
        type: number
        format: decimal
      decimal3:
        type: number
        format: decimal
      decimal4:
        type: number
        format: decimal
      decimal5:
        type: number
        format: decimal
      string1:
        type: string
        maxLength: 255
        minLength: 0
      string2:
        type: string
        maxLength: 255
        minLength: 0
      string3:
        type: string
        maxLength: 255
        minLength: 0
      string4:
        type: string
        maxLength: 255
        minLength: 0
      string5:
        type: string
        maxLength: 255
        minLength: 0
      string6:
        type: string
        maxLength: 255
        minLength: 0
      string7:
        type: string
        maxLength: 255
        minLength: 0
      string8:
        type: string
        maxLength: 255
        minLength: 0
      string9:
        type: string
        maxLength: 255
        minLength: 0
      string10:
        type: string
        maxLength: 255
        minLength: 0
      string11:
        type: string
        maxLength: 255
        minLength: 0
      string12:
        type: string
        maxLength: 255
        minLength: 0
      string13:
        type: string
        maxLength: 255
        minLength: 0
      string14:
        type: string
        maxLength: 255
        minLength: 0
      string15:
        type: string
        maxLength: 255
        minLength: 0
      string16:
        type: string
        maxLength: 255
        minLength: 0
      string17:
        type: string
        maxLength: 255
        minLength: 0
      string18:
        type: string
        maxLength: 255
        minLength: 0
      string19:
        type: string
        maxLength: 255
        minLength: 0
      string20:
        type: string
        maxLength: 255
        minLength: 0
      string21:
        type: string
        maxLength: 255
        minLength: 0
      string22:
        type: string
        maxLength: 255
        minLength: 0
      string23:
        type: string
        maxLength: 255
        minLength: 0
      string24:
        type: string
        maxLength: 255
        minLength: 0
      string25:
        type: string
        maxLength: 255
        minLength: 0
      string26:
        type: string
        maxLength: 255
        minLength: 0
      string27:
        type: string
        maxLength: 255
        minLength: 0
      string28:
        type: string
        maxLength: 255
        minLength: 0
      string29:
        type: string
        maxLength: 255
        minLength: 0
      string30:
        type: string
        maxLength: 255
        minLength: 0
      datetime1:
        type: string
        format: date-time
      datetime2:
        type: string
        format: date-time
      datetime3:
        type: string
        format: date-time
      datetime4:
        type: string
        format: date-time
      datetime5:
        type: string
        format: date-time
      bool1:
        type: boolean
      bool2:
        type: boolean
      bool3:
        type: boolean
      bool4:
        type: boolean
      bool5:
        type: boolean
  UserActiveStatus:
    type: integer
    description: Describes the user active status.
    x-enumNames:
      - Active
      - Inactive
    enum:
      - 0
      - 1
  NonPagedUserResource:
    type: object
    description: >-
      This class is NOT to be used as an actual response. This is to be used as
      the `SwaggerResponse` return type for

      non-paginated endpoints that return UserResource.
    properties:
      id:
        type: string
        description: The unique user identifier.
        format: guid
      departmentId:
        type: string
        description: The user's department ID.
        format: guid
      firstName:
        type: string
        description: The first name.
      middleName:
        type: string
        description: The middle name.
      lastName:
        type: string
        description: The last name.
      username:
        type: string
        description: The unique username.
      password:
        type: string
        description: Password Required for User Creation.
        maxLength: 255
        minLength: 0
      emailAddress:
        type: string
        description: The user contact email address.
      externalId:
        type: string
        description: The external ID used for integrations with external systems.
      ccEmailAddresses:
        type: array
        description: List of email addresses, maximum 5 (MaxCcEmailSize)
        items:
          type: string
      languageId:
        type: integer
        description: The language id.
        format: int32
      gender:
        description: |-
          The gender.

          Possible Enum Values: `0 = None` `1 = Male` `2 = Female`
        allOf:
          - $ref: '#/definitions/Gender'
      address:
        type: string
        description: The mailing address.
      address2:
        type: string
        description: The mailing address, part two.
      city:
        type: string
        description: The city.
      provinceId:
        type: string
        description: The province id.
        format: guid
      countryId:
        type: string
        description: The country id.
        format: guid
      postalCode:
        type: string
        description: The postal/zip code.
      phone:
        type: string
        description: The phone number.
      employeeNumber:
        type: string
        description: The employee number.
      location:
        type: string
        description: The location.
      jobTitle:
        type: string
        description: The job title.
      referenceNumber:
        type: string
        description: The reference number.
      dateHired:
        type: string
        description: If applicable, the date the user was hired.
        format: date-time
      dateTerminated:
        type: string
        description: If applicable, the date the user's employment was terminated.
        format: date-time
      dateEdited:
        type: string
        description: The date the user is edited.
        format: date-time
      dateAdded:
        type: string
        description: The date the user was added.
        format: date-time
      lastLoginDate:
        type: string
        description: The last date the user successfully logged in.
        format: date-time
      notes:
        type: string
        description: The notes.
      customFields:
        description: The custom fields.
        allOf:
          - $ref: '#/definitions/CustomFieldsResource'
      roleIds:
        type: array
        description: The role ids.
        items:
          type: string
          format: guid
      activeStatus:
        description: |-
          The user's active status as UserActiveStatus .

          Possible Enum Values: `0 = Active` `1 = Inactive`
        allOf:
          - $ref: '#/definitions/UserActiveStatus'
      isLearner:
        type: boolean
        description: True if the user is a learner, otherwise false.
      isAdmin:
        type: boolean
        description: True if the user is an administrator, otherwise false.
      isInstructor:
        type: boolean
        description: True if the user is an instructor, otherwise false.
      isManager:
        type: boolean
        description: True if the user is a manager, otherwise false.
      supervisorId:
        type: string
        description: The supervisor id.
        format: guid
      hasUsername:
        type: boolean
        description: Determines whether the user has a username or not.
  GenericUserRequestBody:
    type: object
    description: The request body for updating a user.
    required:
      - username
      - departmentId
      - firstName
      - lastName
    properties:
      username:
        type: string
        description: The user's username.
        maxLength: 255
        minLength: 0
      password:
        type: string
        description: The user's password.
        maxLength: 255
        minLength: 0
      departmentId:
        type: string
        description: The user's department ID.
        format: guid
        minLength: 1
      firstName:
        type: string
        description: The user's first name.
        maxLength: 255
        minLength: 0
      lastName:
        type: string
        description: The user's last name.
        maxLength: 255
        minLength: 0
      middleName:
        type: string
        description: The user's middle name.
        maxLength: 255
        minLength: 0
      address:
        type: string
        description: The user's address.
        maxLength: 4000
        minLength: 0
      address2:
        type: string
        description: The user's second address.
        maxLength: 4000
        minLength: 0
      city:
        type: string
        description: The user's city.
        maxLength: 255
        minLength: 0
      postalCode:
        type: string
        description: The user's postal code.
        maxLength: 255
        minLength: 0
      provinceId:
        type: string
        description: The user's province ID.
        format: guid
      countryId:
        type: string
        description: The user's country ID.
        format: guid
      location:
        type: string
        description: The user's location
        maxLength: 255
        minLength: 0
      dateHired:
        type: string
        description: The date the user was hired.
        format: date-time
      dateTerminated:
        type: string
        description: The date the user was terminated.
        format: date-time
      emailAddress:
        type: string
        description: The user's email address.
        maxLength: 255
        minLength: 0
        pattern: >-
          ^[a-zA-Z0-9!#$%&'*+\/=?^_`{|}~-]+(\.[a-zA-Z0-9!#$%&'*+\/=?^_`{|}~-]+)*@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$
      ccEmailAddresses:
        type: array
        description: The user's cc email addresses.
        items:
          type: string
      employeeNumber:
        type: string
        description: The user's employee number.
        maxLength: 255
        minLength: 0
      supervisorId:
        type: string
        description: The user's supervisor's ID.
        format: guid
      externalId:
        type: string
        description: The user's external ID.
        maxLength: 255
        minLength: 0
      languageId:
        type: integer
        description: The user's default language ID.
        format: int32
      gender:
        description: |-
          The user's gender.

          Possible Enum Values: `0 = None` `1 = Male` `2 = Female`
        allOf:
          - $ref: '#/definitions/Gender'
      activeStatus:
        description: |-
          The user's active status.

          Possible Enum Values: `0 = Active` `1 = Inactive`
        allOf:
          - $ref: '#/definitions/UserActiveStatus'
      jobTitle:
        type: string
        description: The user's job title.
        maxLength: 255
        minLength: 0
      notes:
        type: string
        description: Notes about the user.
      phone:
        type: string
        description: The user's phone number.
        maxLength: 255
        minLength: 0
      referenceNumber:
        type: string
        description: The user's reference number.
        maxLength: 255
        minLength: 0
      roleIds:
        type: array
        description: The user's associated role IDs.
        items:
          type: string
          format: guid
      isLearner:
        type: boolean
        description: True if the user is a learner, otherwise false.
      isInstructor:
        type: boolean
        description: True if the user is an instructor, otherwise false.
      isAdmin:
        type: boolean
        description: True if the user is an admin, otherwise false.
      hasUsername:
        type: boolean
        description: True if the user has a username, otherwise false.
      customFields:
        description: The user's custom fields.
        allOf:
          - $ref: '#/definitions/CustomFieldsDefinition'
  CustomFieldsDefinition:
    type: object
    description: >-
      A common definition of Custom Fields associated with user-related
      resources/requests.
    properties:
      decimal1:
        type: number
        format: decimal
      decimal2:
        type: number
        format: decimal
      decimal3:
        type: number
        format: decimal
      decimal4:
        type: number
        format: decimal
      decimal5:
        type: number
        format: decimal
      string1:
        type: string
      string2:
        type: string
      string3:
        type: string
      string4:
        type: string
      string5:
        type: string
      string6:
        type: string
      string7:
        type: string
      string8:
        type: string
      string9:
        type: string
      string10:
        type: string
      string11:
        type: string
      string12:
        type: string
      string13:
        type: string
      string14:
        type: string
      string15:
        type: string
      string16:
        type: string
      string17:
        type: string
      string18:
        type: string
      string19:
        type: string
      string20:
        type: string
      string21:
        type: string
      string22:
        type: string
      string23:
        type: string
      string24:
        type: string
      string25:
        type: string
      string26:
        type: string
      string27:
        type: string
      string28:
        type: string
      string29:
        type: string
      string30:
        type: string
      datetime1:
        type: string
        format: date-time
      datetime2:
        type: string
        format: date-time
      datetime3:
        type: string
        format: date-time
      datetime4:
        type: string
        format: date-time
      datetime5:
        type: string
        format: date-time
      bool1:
        type: boolean
      bool2:
        type: boolean
      bool3:
        type: boolean
      bool4:
        type: boolean
      bool5:
        type: boolean
  UpsertUserResource:
    type: object
    description: Represents information about an upserted user in the Integration API.
    properties:
      id:
        type: string
        description: The user's ID.
        format: guid
      username:
        type: string
        description: The user's username.
        maxLength: 255
        minLength: 0
  UploadUsersKey:
    type: integer
    description: >-
      Represents the identifier that will be used in users/upload?key={key}. The
      chosen key's property will become required.
    x-enumNames:
      - Username
      - ExternalId
    enum:
      - 0
      - 1
  UploadUsersResource:
    type: object
    description: >-
      Represents information about an uploaded (created/updated) user in the
      Integration API.
    properties:
      key:
        type: string
        description: The user's unique ID.
      value:
        type: string
        description: The user property (key/correlation) used to create/update a user.
  UpdateUserManagementSettingsRequestBody2:
    type: object
    description: The request body for updating user management settings.
    required:
      - userManagementType
    properties:
      userManagementType:
        description: |-
          The type of admin the user should be updated to.

          Possible Enum Values: `1 = Group` `2 = Department` `3 = All`
        allOf:
          - $ref: '#/definitions/UserManagementType2'
      managedGroupId:
        type: string
        description: The id of the group the user should manage.
        format: guid
      managedDepartments:
        type: array
        description: The list of departments the user should manage.
        items:
          $ref: '#/definitions/DepartmentSelectionDefinition'
  UserManagementType2:
    type: integer
    description: The allowable user management types.
    x-enumNames:
      - Group
      - Department
      - All
    enum:
      - 1
      - 2
      - 3
  CompetenciesResource:
    type: object
    description: List of CompetencyResources.
    properties:
      totalItems:
        type: integer
        description: The total number of items in the unbounded collection
        format: int32
      returnedItems:
        type: integer
        description: The number of items in the bound collection being returned
        format: int32
      limit:
        type: integer
        description: The current page size for the collection
        format: int32
      offset:
        type: integer
        description: The current offset for the collection
        format: int32
      competencies:
        type: array
        description: A list of CompetencyResources.
        items:
          $ref: '#/definitions/CompetencyResource'
  CompetencyResource:
    type: object
    description: A resource representing a competency.
    properties:
      id:
        type: string
        description: |-
          `Filterable` 

          The competency ID.
        format: guid
      name:
        type: string
        description: |-
          `Sortable` `Filterable` 

          The competency name.
      competencyLevel:
        type: integer
        description: |-
          `Sortable` `Filterable` 

          The competency level.
        format: int32
      categoryName:
        type: string
        description: |-
          `Sortable` `Filterable` 

          The competency's category name.
      dateAcquired:
        type: string
        description: |-
          `Sortable` `Filterable` 

          The date the competency was acquired.
        format: date-time
securityDefinitions:
  api_key:
    type: apiKey
    name: x-api-key
    in: header
tags:
  - name: User Management Settings
  - name: Users
x-servers:
  - url: https://rest.myabsorb.com
    description: US
  - url: https://rest.myabsorb.ca
    description: CA
  - url: https://rest.myabsorb.eu
    description: EU
  - url: https://rest.myabsorb.com.au
    description: AU
