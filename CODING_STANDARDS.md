# Coding Standards and Project Structure

This document outlines the comprehensive coding standards, formatting rules, and project structure guidelines for the PsychScene Bridge NestJS project.

## Table of Contents

- [Code Formatting Rules](#code-formatting-rules)
- [Code Quality Standards](#code-quality-standards)
- [Project Structure](#project-structure)
- [Naming Conventions](#naming-conventions)
- [Pre-commit Hooks](#pre-commit-hooks)
- [Development Workflow](#development-workflow)

## Code Formatting Rules

### Prettier Configuration

Our project uses <PERSON><PERSON><PERSON> for consistent code formatting with the following rules:

- **Print Width**: 100 characters maximum per line
- **Indentation**: 2 spaces (no tabs)
- **Semicolons**: Always required
- **Quotes**: Single quotes for strings
- **Trailing Commas**: ES5 compatible
- **Bracket Spacing**: Enabled
- **Arrow Function Parentheses**: Always include

### Comment Formatting

```typescript
// Single-line comments should be concise and descriptive

/**
 * Multi-line comments should follow JSDoc format
 * @param param Description of parameter
 * @returns Description of return value
 */
```

## Code Quality Standards

### ESLint Rules

Our ESLint configuration enforces:

- **Complexity**: Maximum cyclomatic complexity of 10
- **Function Length**: Maximum 50 lines per function
- **File Length**: Maximum 300 lines per file
- **Function Parameters**: Maximum 4 parameters
- **Nesting Depth**: Maximum 4 levels

### TypeScript Strict Mode

- `strict: true` - Enables all strict type checking options
- `noImplicitAny: true` - Disallow implicit any types
- `noUnusedLocals: true` - Report unused local variables
- `noUnusedParameters: true` - Report unused parameters
- `noImplicitReturns: true` - Ensure all code paths return a value

### Code Coverage Requirements

- **Minimum Coverage**: 80% for all metrics
- **Branches**: 80%
- **Functions**: 80%
- **Lines**: 80%
- **Statements**: 80%

## Project Structure

```
src/
├── app.module.ts              # Root application module
├── main.ts                    # Application entry point
├── common/                    # Shared utilities and common code
│   ├── decorators/           # Custom decorators
│   ├── filters/              # Exception filters
│   ├── guards/               # Authentication/authorization guards
│   ├── interceptors/         # Request/response interceptors
│   ├── pipes/                # Validation pipes
│   └── utils/                # Utility functions
├── config/                    # Configuration files
│   ├── database.config.ts    # Database configuration
│   ├── app.config.ts         # Application configuration
│   └── validation.schema.ts  # Environment validation
├── modules/                   # Feature modules
│   ├── auth/                 # Authentication module
│   │   ├── auth.controller.ts
│   │   ├── auth.service.ts
│   │   ├── auth.module.ts
│   │   ├── dto/              # Data Transfer Objects
│   │   ├── entities/         # Database entities
│   │   └── strategies/       # Passport strategies
│   └── users/                # Users module
│       ├── users.controller.ts
│       ├── users.service.ts
│       ├── users.module.ts
│       ├── dto/
│       └── entities/
└── database/                  # Database related files
    ├── migrations/           # Database migrations
    ├── seeds/                # Database seeders
    └── factories/            # Entity factories

test/                          # Test files
├── unit/                     # Unit tests
├── integration/              # Integration tests
├── e2e/                      # End-to-end tests
└── fixtures/                 # Test data fixtures
```

## Naming Conventions

### Files and Directories

- **Files**: Use kebab-case (e.g., `user-profile.service.ts`)
- **Directories**: Use kebab-case (e.g., `user-management/`)
- **Test files**: Add `.spec.ts` suffix (e.g., `user.service.spec.ts`)
- **E2E tests**: Add `.e2e-spec.ts` suffix (e.g., `auth.e2e-spec.ts`)

### TypeScript Code

- **Classes**: PascalCase (e.g., `UserService`, `AuthController`)
- **Interfaces**: PascalCase with 'I' prefix (e.g., `IUserRepository`)
- **Types**: PascalCase (e.g., `UserRole`, `ApiResponse`)
- **Enums**: PascalCase (e.g., `UserStatus`, `HttpStatus`)
- **Variables**: camelCase (e.g., `userName`, `isActive`)
- **Functions**: camelCase (e.g., `getUserById`, `validateInput`)
- **Constants**: SCREAMING_SNAKE_CASE (e.g., `MAX_RETRY_ATTEMPTS`)

### NestJS Specific

- **Controllers**: `*.controller.ts` (e.g., `users.controller.ts`)
- **Services**: `*.service.ts` (e.g., `users.service.ts`)
- **Modules**: `*.module.ts` (e.g., `users.module.ts`)
- **DTOs**: `*.dto.ts` (e.g., `create-user.dto.ts`)
- **Entities**: `*.entity.ts` (e.g., `user.entity.ts`)
- **Guards**: `*.guard.ts` (e.g., `jwt-auth.guard.ts`)
- **Pipes**: `*.pipe.ts` (e.g., `validation.pipe.ts`)
- **Interceptors**: `*.interceptor.ts` (e.g., `logging.interceptor.ts`)

## Pre-commit Hooks

Our pre-commit hooks automatically run:

1. **ESLint**: Fixes linting issues automatically
2. **Prettier**: Formats code according to our standards
3. **Type Checking**: Validates TypeScript types
4. **Tests**: Runs related tests for changed files

### Manual Quality Checks

```bash
# Run all quality checks
npm run quality:check

# Fix formatting and linting issues
npm run quality:fix

# Individual checks
npm run typecheck
npm run lint:check
npm run format:check
npm run test:cov:ci
```

## Development Workflow

### Before Starting Development

1. Ensure all dependencies are installed: `npm install`
2. Run quality checks: `npm run quality:check`
3. Start development server: `npm run start:dev`

### Before Committing

1. Run quality checks: `npm run quality:check`
2. Fix any issues: `npm run quality:fix`
3. Ensure tests pass: `npm run test:cov`
4. Commit changes (pre-commit hooks will run automatically)

### Module Development Guidelines

1. **Single Responsibility**: Each module should have a single, well-defined purpose
2. **Dependency Injection**: Use NestJS DI container for all dependencies
3. **Error Handling**: Implement proper exception handling with custom filters
4. **Validation**: Use DTOs with class-validator for input validation
5. **Documentation**: Document all public APIs with JSDoc comments
6. **Testing**: Maintain minimum 80% code coverage

### API Development Standards

1. **RESTful Design**: Follow REST principles for API endpoints
2. **Versioning**: Use `/api/v1/` prefix for all endpoints
3. **Response Format**: Consistent JSON response structure
4. **Error Responses**: Standardized error response format
5. **Swagger Documentation**: Document all endpoints with OpenAPI decorators

## Asset Management

### Static Assets

- Store in `public/` directory
- Use descriptive names with proper extensions
- Optimize images before committing
- Use SVG format for icons when possible

### Environment Configuration

- Use `.env` files for environment-specific configuration
- Never commit sensitive data to version control
- Document all environment variables in `.env.example`
- Validate environment variables using configuration schemas

## Documentation Standards

### Code Documentation

- Use JSDoc for all public methods and classes
- Include parameter descriptions and return types
- Document complex business logic with inline comments
- Keep README.md updated with setup and usage instructions

### API Documentation

- Use Swagger/OpenAPI decorators for all endpoints
- Provide example requests and responses
- Document all possible error scenarios
- Include authentication requirements

---

**Note**: This document should be reviewed and updated regularly as the project evolves. All team members are expected to follow these standards to ensure code quality and maintainability.
