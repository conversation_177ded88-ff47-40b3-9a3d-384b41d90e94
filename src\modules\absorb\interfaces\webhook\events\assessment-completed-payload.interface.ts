import { WebhookEventType } from '@/modules/absorb/enums';
import { BaseWebhookPayload } from '../absorb-base-webhook-payload.interface';

/**
 * Assessment completion payload
 */
export interface AssessmentCompletedPayload extends BaseWebhookPayload {
  readonly eventType: WebhookEventType.ASSESSMENT_COMPLETED;
  readonly assessmentId: string; // GUID - assessment identifier
  readonly score: number; // decimal percentage
  readonly attempts: number; // int32 - attempt count
  readonly passed: boolean; // whether assessment was passed
  readonly dateCompleted: string; // ISO 8601 datetime
}
